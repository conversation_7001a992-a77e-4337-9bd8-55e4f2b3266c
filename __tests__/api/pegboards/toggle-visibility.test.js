import adminClient from '../../../graphql/adminClient';
import pgPoolQuery from '../../../utils/db/pgQuery';
import rollbar from '../../../utils/rollbar';

// Mock dependencies
jest.mock('../../../graphql/adminClient');
jest.mock('../../../utils/db/pgQuery');
jest.mock('../../../utils/rollbar');

// Mock userGuardCheck to bypass authentication
jest.mock('../../../utils/auth/userGuardCheck', () => {
  return jest.fn((handler) => handler);
});

// Import the API handler after mocking
import togglePegboardVisibility from '../../../pages/api/pegboards/toggle-visibility';

describe('/api/pegboard/toggle-visibility', () => {
  let req, res;

  beforeEach(() => {
    // Mock request and response objects
    req = {
      body: {
        pegboardId: 'test-pegboard-uuid',
        isHidden: true,
        userId: 'test-user-id',
      },
    };

    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };

    // Mock rollbar methods
    rollbar.info = jest.fn();
    rollbar.error = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Input validation', () => {
    it('should accept valid input', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL for getting current hidden pegboards (empty array)
      pgPoolQuery
        .mockResolvedValueOnce([]) // No existing user_informations record
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['test-pegboard-uuid'],
          },
        ]); // Updated user_informations with hidden pegboard

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard hidden from your view',
          data: expect.objectContaining({
            id: 'test-pegboard-uuid',
            name: 'Test Pegboard',
            isHidden: true,
            hiddenPegboards: ['test-pegboard-uuid'],
          }),
        }),
      );
    });

    it('should reject missing pegboardId', async () => {
      req.body = { isHidden: true, userId: 'test-user-id' };

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 0,
          error: expect.stringContaining('pegboardId'),
        }),
      );
    });

    it('should reject missing isHidden', async () => {
      req.body = {
        pegboardId: 'test-pegboard-uuid',
        userId: 'test-user-id',
      };

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 0,
          error: expect.stringContaining('isHidden'),
        }),
      );
    });

    it('should reject missing userId', async () => {
      req.body = { pegboardId: 'test-pegboard-uuid', isHidden: true };

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 0,
          error: expect.stringContaining('userId'),
        }),
      );
    });

    it('should reject invalid isHidden type', async () => {
      req.body = {
        pegboardId: 'test-pegboard-uuid',
        isHidden: 'invalid',
        userId: 'test-user-id',
      };

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 0,
          error: expect.stringContaining('boolean'),
        }),
      );
    });
  });

  describe('Pegboard existence', () => {
    it('should handle non-existent pegboard', async () => {
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: null,
      });

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.send).toHaveBeenCalledWith({
        status: 0,
        message: 'Pegboard not found',
      });
    });
  });

  describe('State management', () => {
    it('should handle already hidden pegboard', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - pegboard already in hidden array
      pgPoolQuery.mockResolvedValueOnce([
        {
          pegboard_hidden: ['test-pegboard-uuid'], // Already hidden
        },
      ]);

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard is already hidden for this user',
          data: expect.objectContaining({
            id: 'test-pegboard-uuid',
            name: 'Test Pegboard',
            isHidden: true,
          }),
        }),
      );

      // Should only call PostgreSQL once to get current state, not to update
      expect(pgPoolQuery).toHaveBeenCalledTimes(1);
    });

    it('should handle showing pegboard', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      req.body.isHidden = false;

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - pegboard currently in hidden array
      pgPoolQuery
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['test-pegboard-uuid', 'other-pegboard-uuid'],
          },
        ])
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['other-pegboard-uuid'], // Removed test-pegboard-uuid
          },
        ]);

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard visible in your view',
          data: expect.objectContaining({
            id: 'test-pegboard-uuid',
            name: 'Test Pegboard',
            isHidden: false,
            hiddenPegboards: ['other-pegboard-uuid'],
          }),
        }),
      );
    });
  });

  describe('Error handling', () => {
    it('should handle GraphQL database errors', async () => {
      adminClient.request.mockRejectedValue(
        new Error('GraphQL database error'),
      );

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(rollbar.error).toHaveBeenCalledWith(
        'Error toggling pegboard visibility',
        expect.objectContaining({
          error: 'GraphQL database error',
          pegboardId: 'test-pegboard-uuid',
          userId: 'test-user-id',
          isHidden: true,
        }),
      );
    });

    it('should handle PostgreSQL database errors', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock successful GraphQL call
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL error on first call (getting hidden pegboards)
      pgPoolQuery.mockRejectedValue(new Error('PostgreSQL connection error'));

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(rollbar.error).toHaveBeenCalledWith(
        'Error toggling pegboard visibility',
        expect.objectContaining({
          error: 'PostgreSQL connection error',
        }),
      );
    });
  });

  describe('Performance', () => {
    it('should minimize database calls', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL calls
      pgPoolQuery
        .mockResolvedValueOnce([]) // No existing user_informations record
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['test-pegboard-uuid'],
          },
        ]); // Updated user_informations with hidden pegboard

      await togglePegboardVisibility(req, res);

      // Should make exactly 1 GraphQL call and 2 PostgreSQL calls (get + update)
      expect(adminClient.request).toHaveBeenCalledTimes(1);
      expect(pgPoolQuery).toHaveBeenCalledTimes(2);
    });

    it('should not make unnecessary updates for same state', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - pegboard already in hidden array
      pgPoolQuery.mockResolvedValueOnce([
        {
          pegboard_hidden: ['test-pegboard-uuid'], // Already hidden
        },
      ]);

      await togglePegboardVisibility(req, res);

      // Should make 1 GraphQL call and 1 PostgreSQL call (only to check state, no update)
      expect(adminClient.request).toHaveBeenCalledTimes(1);
      expect(pgPoolQuery).toHaveBeenCalledTimes(1);
    });
  });

  describe('Array management', () => {
    it('should add pegboard to empty hidden array', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - no existing user_informations record
      pgPoolQuery
        .mockResolvedValueOnce([]) // No existing record
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['test-pegboard-uuid'],
          },
        ]); // New record with hidden pegboard

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard hidden from your view',
          data: expect.objectContaining({
            hiddenPegboards: ['test-pegboard-uuid'],
          }),
        }),
      );
    });

    it('should add pegboard to existing hidden array', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - existing hidden pegboards
      pgPoolQuery
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['other-pegboard-uuid'],
          },
        ])
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['other-pegboard-uuid', 'test-pegboard-uuid'],
          },
        ]);

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard hidden from your view',
          data: expect.objectContaining({
            hiddenPegboards: ['other-pegboard-uuid', 'test-pegboard-uuid'],
          }),
        }),
      );
    });

    it('should remove pegboard from hidden array', async () => {
      // Clear previous mocks
      jest.clearAllMocks();

      req.body.isHidden = false;

      // Mock GraphQL for getting pegboard info
      adminClient.request.mockResolvedValueOnce({
        pegboard_by_pk: {
          id: 'test-pegboard-uuid',
          name: 'Test Pegboard',
          creator_id: 'creator-uuid',
        },
      });

      // Mock PostgreSQL - pegboard in hidden array
      pgPoolQuery
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['test-pegboard-uuid', 'other-pegboard-uuid'],
          },
        ])
        .mockResolvedValueOnce([
          {
            pegboard_hidden: ['other-pegboard-uuid'],
          },
        ]);

      await togglePegboardVisibility(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 1,
          message: 'Pegboard visible in your view',
          data: expect.objectContaining({
            hiddenPegboards: ['other-pegboard-uuid'],
          }),
        }),
      );
    });
  });
});
