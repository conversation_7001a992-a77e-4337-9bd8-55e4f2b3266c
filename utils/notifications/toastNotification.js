import { toast } from 'react-toastify'
import constantOptions from '../../constants/constantOptions'
import PegboardCreated from '../../components/toast-notification/PegboardCreated'
import CustomToast from '../../components/toast-notification/CustomToast'
import RequestToast from '../../components/toast-notification/RequestToast'

const toastNotification = ({ type = '', message = '' }) => {
    const {
        TOAST_TYPE: { ERROR, SUCCESS, WARNING, GROUP_LINK_COPIED, PEGBOARD_CREATED, REQUEST_MOVED },
    } = constantOptions

    let notificationStyle = {
        position: 'bottom-right',
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: 'colored',
        zIndex: 9999999,
    }

    let newStyle = {
        position: 'bottom-right',
        className: 'custom-toast',
        autoClose: 10000,
        hideProgressBar: false,
    }
    let pegboard = {
        position: 'bottom-right',
        className: 'pegboard-toast',
        autoClose: 10000,
        hideProgressBar: false,
    }
    let request = {
        position: 'bottom-right',
        className: 'request-toast',
        autoClose: 10000,
        hideProgressBar: true,
    }

    switch (type) {
        case ERROR: {
            return toast.error(message, notificationStyle)
        }
        case SUCCESS: {
            return toast.success(message, notificationStyle)
        }
        case WARNING: {
            return toast.warn(message, notificationStyle)
        }
        case GROUP_LINK_COPIED: {
            return toast(<CustomToast message={"Link copied to your clipboard"} />, newStyle)
        }
        case PEGBOARD_CREATED: {
            return toast(<CustomToast message={message} />, pegboard)
        }
        case REQUEST_MOVED: {
            return toast(<RequestToast message={message} />, request)
        }
        case PEGBOARD_CREATED: {
            return toast(PegboardCreated, pegboard)
        }
    }
}
export default toastNotification
