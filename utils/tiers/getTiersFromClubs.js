import constantOptions from '../../constants/constantOptions';
import adminClient from '../../graphql/adminClient';

const { FERN, SAGE, MOSS, OLIVE, TEAL } = constantOptions?.CLUB_TIERS;

const USER_CLUBS = `
query getUserClubs($user_id: uuid!) {
    user(where: {id: {_eq: $user_id}}) {
        visibleToPublic
        clubs {
            muted
            proximity
            restricted_membership
            club {
                lowest_visible_tier
            }
        }
    }
}
`;

const USER_VISIBILITY = `
query user($user_id: uuid!) {
    user_by_pk(id: $user_id) {
        visibleToPublic
        private_network_user {
            private_network_id
        }
    }
}`;

const IS_USER_SUPER_HOST = `
query user($user_id: uuid!) {
    user_by_pk(id: $user_id) {
        is_super_host
    }
}`;

export async function getClubsOfUser(user_id) {
  /* Get clubs of a user - especially for calculating tier in the below function (getTierFromClubs) */

  const { user } = await adminClient.request(USER_CLUBS, {
    user_id,
  });

  if (user?.[0]?.clubs?.length) {
    return user[0].clubs;
  } else {
    return [];
  }
}

export default async function getTierFromClubs(clubs, user_id) {
  const isTg = await isUserTg(user_id);
  if (!isTg) {
    return TEAL;
  }

  //Check if user is super host
  const user = await adminClient.request(IS_USER_SUPER_HOST, {
    user_id,
  });

  let lowestTier = OLIVE;

  clubs.map(({ club, proximity, restricted_membership, muted = false }) => {
    club.lowest_visible_tier =
      club?.new && !club.lowest_visible_tier ? OLIVE : club.lowest_visible_tier;

    if (!muted) {
      if (
        club?.lowest_visible_tier < lowestTier &&
        proximity !== constantOptions.PROXIMITY.LONG_DISTANCE &&
        !restricted_membership
      ) {
        lowestTier = club.lowest_visible_tier;
      }

      if (user?.user_by_pk?.is_super_host) {
        lowestTier = club.lowest_visible_tier;
      }
    }
  });

  return lowestTier === 0 ? FERN : lowestTier;
}

export async function isUserTg(user_id) {
  try {
    const { user_by_pk } = await adminClient.request(USER_VISIBILITY, {
      user_id,
    });
    if (!user_by_pk?.private_network_user || user_by_pk?.visibleToPublic) {
      return true;
    } else if (
      !user_by_pk?.visibleToPublic &&
      !user_by_pk?.private_network_user
    ) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log('An error occurred', error);
    return false;
  }
}
