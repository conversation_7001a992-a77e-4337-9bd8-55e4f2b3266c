import constantOptions from '../../constants/constantOptions';
import getStreamInstance from '../chat-v2/getStreamInstance';

const {
  STREAM_CHANNEL_TYPES: { REQUEST_CHAT_GROUP },
} = constantOptions;

async function sendRequestSystemMessage({
  stream_channel_id,
  systemChatMessage,
  gameInformation,
}) {
  let channel = null;
  if (stream_channel_id) {
    const serverClient = getStreamInstance();
    const channel = serverClient.channel(REQUEST_CHAT_GROUP, stream_channel_id);

    const message = {
      text: systemChatMessage,
      type: 'system',
      user: { id: 'system' },
    };

    if (gameInformation) {
      message.text = '';
      message.game_information = gameInformation;
    }

    await channel.sendMessage(message);
  }
  return channel;
}

export default sendRequestSystemMessage;
