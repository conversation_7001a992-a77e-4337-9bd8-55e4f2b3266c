import moment from "moment";
import streamOptions from "../../constants/streamOptions";
import debounce from "../helper/debounce";

const { SORT, STANDARD_FILTERS, OPTIONS, TYPES } = streamOptions?.CHANNEL;
const { ONE_TO_ONE, SYSTEM_THOUSAND_GREENS_PUBLIC, SYSTEM_PRIVATE_NETWORK, USER_CREATED_GROUP, MY_TG_GROUP } = TYPES

/**
 * This function uses debounce method to search the channels in stream
 * which we are consuming in the main channel list as of now
 */

const customChannelSearchWithDelay = debounce(
    async ({ setResults, setSearching }, value, userId, streamChatClient, selectedChatType) => {
        let searchFilters = STANDARD_FILTERS(userId);
        if (value || value === 0) {

            const baseFilter = {
                members: { $in: [userId] },
                hidden: { $eq: false },
            };
            const groupTypes = [
                streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.SYSTEM_PRIVATE_NETWORK,
                streamOptions.CHANNEL.TYPES.SYSTEM_THOUSAND_GREENS_PUBLIC,
                streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.MY_TG_GROUP
            ];
            switch (selectedChatType) {
                case 'dm':
                    searchFilters = {
                        ...baseFilter,
                        'member.user.name': { $autocomplete: value },
                        type: { $eq: streamOptions.CHANNEL.TYPES.ONE_TO_ONE },
                        last_message_at: { $lte: moment().add(2, 'hours') }
                    };
                    break;
                case 'group':
                    searchFilters = {
                        ...baseFilter,
                        name: { $autocomplete: value },
                        type: { $in: groupTypes }
                    };
                    break;
                case 'all':
                    searchFilters = {
                        members: { $in: [userId] },
                        $or: [
                            {
                                'member.user.name': { $autocomplete: value },
                                last_message_at: { $lte: moment() },
                                type: ONE_TO_ONE,

                            },
                            {
                                name: { $autocomplete: value },
                                type: { $in: [SYSTEM_PRIVATE_NETWORK, USER_CREATED_GROUP, SYSTEM_THOUSAND_GREENS_PUBLIC, MY_TG_GROUP] },
                            }
                        ]
                    }
            }

            let channels = await streamChatClient.queryChannels(searchFilters, SORT, OPTIONS);

            channels = channels.filter(channel => {
                if (channel?.type === ONE_TO_ONE) {
                    if (channel?.state?.messages?.length) {
                        let otherName = Object.values(channel?.state?.members).find(member => member?.user_id !== userId)
                        return otherName?.user?.name?.toLowerCase()?.includes(value?.toLowerCase())
                    }
                    return false;
                }
                return true;
            })

            setResults(channels);
            setSearching(false);
        }
    }, 400);

export default customChannelSearchWithDelay;