let newrelic = null;

// Initialize New Relic only on server side
if (typeof window === 'undefined' && process.env.NEW_RELIC_LICENSE_KEY) {
  try {
    newrelic = require('newrelic');
  } catch (error) {
    console.warn('New Relic could not be loaded:', error.message);
  }
}

/**
 * Add custom attributes to the current transaction
 * @param {Object} attributes - Key-value pairs to add as custom attributes
 */
export const addCustomAttributes = (attributes) => {
  if (newrelic && typeof attributes === 'object') {
    Object.entries(attributes).forEach(([key, value]) => {
      newrelic.addCustomAttribute(key, value);
    });
  }
};

/**
 * Record a custom event
 * @param {string} eventType - The type of event
 * @param {Object} attributes - Event attributes
 */
export const recordCustomEvent = (eventType, attributes = {}) => {
  if (newrelic) {
    newrelic.recordCustomEvent(eventType, attributes);
  }
};

/**
 * Record a metric
 * @param {string} name - Metric name
 * @param {number} value - Metric value
 */
export const recordMetric = (name, value) => {
  if (newrelic) {
    newrelic.recordMetric(name, value);
  }
};

/**
 * Notice an error
 * @param {Error} error - The error to report
 * @param {Object} customAttributes - Additional attributes
 */
export const noticeError = (error, customAttributes = {}) => {
  if (newrelic) {
    newrelic.noticeError(error, customAttributes);
  }
};

/**
 * Set the name of the current transaction
 * @param {string} category - Transaction category (e.g., 'Web', 'Background')
 * @param {string} name - Transaction name
 */
export const setTransactionName = (category, name) => {
  if (newrelic) {
    newrelic.setTransactionName(category, name);
  }
};

/**
 * Start a web transaction
 * @param {string} url - The URL for the transaction
 * @param {Function} handle - The function to execute within the transaction
 */
export const startWebTransaction = (url, handle) => {
  if (newrelic) {
    return newrelic.startWebTransaction(url, handle);
  }
  return handle();
};

/**
 * Start a background transaction
 * @param {string} name - The name of the background transaction
 * @param {string} group - The group name (optional)
 * @param {Function} handle - The function to execute within the transaction
 */
export const startBackgroundTransaction = (name, group, handle) => {
  if (newrelic) {
    if (typeof group === 'function') {
      handle = group;
      group = null;
    }
    return newrelic.startBackgroundTransaction(name, group, handle);
  }
  return handle();
};

/**
 * Get the current transaction
 */
export const getTransaction = () => {
  if (newrelic) {
    return newrelic.getTransaction();
  }
  return null;
};

/**
 * End the current transaction
 */
export const endTransaction = () => {
  if (newrelic) {
    const transaction = newrelic.getTransaction();
    if (transaction) {
      transaction.end();
    }
  }
};

/**
 * Ignore the current transaction
 */
export const ignoreTransaction = () => {
  if (newrelic) {
    newrelic.ignoreTransaction();
  }
};

/**
 * Add user tracking to the current transaction
 * @param {string} userId - The user ID
 * @param {string} userEmail - The user email (optional)
 * @param {string} userName - The user name (optional)
 */
export const setUserAttributes = (userId, userEmail = null, userName = null) => {
  if (newrelic && userId) {
    const attributes = { userId };
    if (userEmail) attributes.userEmail = userEmail;
    if (userName) attributes.userName = userName;
    addCustomAttributes(attributes);
  }
};

/**
 * Track API performance
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method
 * @param {number} responseTime - Response time in milliseconds
 * @param {number} statusCode - HTTP status code
 */
export const trackApiPerformance = (endpoint, method, responseTime, statusCode) => {
  if (newrelic) {
    recordCustomEvent('ApiCall', {
      endpoint,
      method,
      responseTime,
      statusCode,
      timestamp: Date.now()
    });
    
    recordMetric(`Custom/API/${method}/${endpoint}/ResponseTime`, responseTime);
    recordMetric(`Custom/API/${method}/${endpoint}/Calls`, 1);
  }
};

export default newrelic;
