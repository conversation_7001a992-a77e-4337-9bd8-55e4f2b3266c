import pgPoolQuery from '../db/pgQuery';
import sleep from '../helper/sleep';
import rollbar from '../rollbar';
import { sendFailureMail } from '../../pages/api/cron/dailyCron';

export default async function updateDashboardStats() {
  try {
    const query = `
            UPDATE dashboard_stats
            SET 
                new_active_members = (
                    SELECT COUNT(*)
                    FROM "user"
                    WHERE created_at >= CURRENT_DATE - INTERVAL '4 weeks'
                    AND account_activated = true
                ),
                new_clubs = GREATEST(1, (
                    SELECT COUNT(*)
                    FROM courses c
                    LEFT JOIN user_club uc on uc.club_id = c.id
                    LEFT JOIN "user" u on u.id = uc.user_id
                    WHERE c.created_at >= CURRENT_DATE - INTERVAL '4 weeks'
                    AND club_type = 0
                    AND address IS NOT NULL
                    AND uc.user_id IS NOT NULL
                    AND u.account_activated = true
                    AND u.deactivated = false
                )),
                requests_made = (
                    SELECT COUNT(*)
                    FROM request
                    WHERE created_at >= CURRENT_DATE - INTERVAL '4 weeks'
                ),
                requests_accepted = (
                    SELECT COUNT(*)
                    FROM game
                    WHERE created_at >= CURRENT_DATE - INTERVAL '4 weeks'
                ),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = 1
            RETURNING *;
        `;

    const result = await pgPoolQuery(query);
    return result[0];
  } catch (error) {
    const errorType = {
      functionName: updateDashboardStats.name,
      errorMsg: error.toString(),
    };

    rollbar.error(`Cron:`, errorType);
    await sendFailureMail(errorType);

    return errorType;
  } finally {
    rollbar.info('Cron: -- Updated Dashboard Stats --');
    console.log('-- Updated Dashboard Stats --');
  }
}
