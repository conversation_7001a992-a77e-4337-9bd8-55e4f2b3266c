import React, { useContext, useState } from 'react'
import { DrawerContext } from '../../context/DrawerContext'
import constantOptions from '../../constants/constantOptions'
import { UserContext } from '../../pages/_app'
import dateFormatter from '../../utils/helper/dateFormatter'
import RequestIcon from '../icons/RequestIcon'
import DeleteIcon from '../icons/DeleteIcon'
import EditIcon from '../icons/EditIcon'
import { ModalContext } from '../../context/ModalContext'
import { getTier } from '../../utils/tiers'
import ENDPOINTS from '../../constants/endpoints'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'
import toastNotification from '../../utils/notifications/toastNotification'

const { OFFER_DETAILS } = constantOptions?.DRAWER_TYPE

const OfferListItem = ({ offer, club, setRefreshMap, showCreateRequestPopup, showAcceptRequestPopup, setShowCreateRequestPopup, setShowAcceptRequestPopup }) => {
  const { user } = useContext(UserContext)
  const { setDrawer } = useContext(DrawerContext)
  const { setModal } = useContext(ModalContext)
  const { maintenanceStatus } = useMaintenanceStatus()
  async function handleCreateRequest(offer) {

    let body = {
      user_id: user.id,
    }
    if (offer?.creatorIsFriend) {
      body = { ...body, isRequestToAll: false }
    }
    fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })
      .then((data) => data.json())
      .then((data) => {
        if (!data.canCreate) {
          setModal({
            title: 'Request Limit',
            message: data.message,
            type: 'warning',
          });
        } else {
          if (showCreateRequestPopup) {
            setModal({
              title: '',
              width: 475,
              type: 'request-confirmation-against-offer',
              offer,
              club,
              setShowCreateRequestPopup: (val) => setShowCreateRequestPopup(val)
            });
          } else {
            setModal({
              creatorIsFriend: offer?.creatorIsFriend,
              requestType: "OFFER",
              title: 'Request against Offer',
              refresh: () => setRefreshMap(prev => prev + 1),
              img: {
                src: '/svg/request-offer.svg',
                style: {
                  height: 80,
                  marginBottom: 10,
                },
              },
              width: 475,
              type: 'request-against-offer',
              offer,
              club,
              textSize: '24px',
              additionalStyles: {
                fontWeight: 400
              },
              refresh: () => setRefreshMap(prev => prev + 1)
            });
          }
        }
      })
      .catch(err => console.log('Error'));
  }

  return (
    <div className='flex flex-col py-md px-md bg-white rounded-lg mb-sm cursor-pointer hover:bg-tealTierBg hover:shadow-lg'
      onClick={() => {
        setDrawer({
          type: OFFER_DETAILS,
          offer,
          clubId: club?.id,
        })
      }}
    >
      <div className='flex pb-sm justify-between'>
        <div className='flex items-center'>
          <div className='h-[40px] w-[40px] flex-center rounded-full bg-tealTierBg'>
            <img width={20} src="/svg/OfferNew.svg" alt="Offer" />
          </div>
          <div className='ml-sm'>
            <div className='text-16'>
              Offer ID <span className='text-darkteal'>#{offer?.offer_id}</span>
            </div>
            <div className='text-gray'>
              {dateFormatter(offer?.start_date, offer?.end_date, constantOptions?.DATE_FORMAT_Do_MM_YYYY)}
            </div>
          </div>
        </div>
        <div className="flex justify-end pr-sm">
          {offer.user_id === user?.id ? (
            <>
              <div
                onClick={(e) => {
                  e.stopPropagation(); // Prevent drawer from opening
                  setModal({
                    title: 'Edit Offer',
                    img: {
                      src: '/svg/offer-outline.svg',
                      style: {
                        height: 48,
                        marginBottom: 10,
                      },
                    },
                    width: 829,
                    type: 'offer',
                    offer: { ...offer, club_name: club?.name },
                    club,
                    source: offer?.my_tg_group_id?.length ? 'groups' : '',
                    refresh: () => setRefreshMap(prev => prev + 1)
                  });
                }}
                className="rounded-lg bg-lightestgray text-gray cursor-pointer flex-center mr-sm hover:bg-darkteal"
                style={{ height: 30, width: 30 }}
              >
                <EditIcon />
              </div>
              <DeleteIcon onClick={(e) => {
                e.stopPropagation(); // Prevent drawer from opening
                setModal({
                  title: 'Delete Offer',
                  width: 475,
                  type: 'delete-offer',
                  offer,
                  club,
                })
              }} />
            </>
          ) : offer?.requested ? (
            <div>
              <div
                className="px-4 py-1 rounded text-white bg-darkteal"
                onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
              >
                {'Requested'}
              </div>
            </div>
          ) : (
            <div
              onClick={(e) => {
                if (maintenanceStatus?.request) {
                  toastNotification({
                    type: constantOptions.TOAST_TYPE.ERROR,
                    message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                  })
                } else {
                  handleCreateRequest(offer)
                }
                e.stopPropagation(); // Prevent drawer from opening
              }}
              className="rounded-lg cursor-pointer flex-center mr-sm"
              style={{ height: 30, width: 30 }}
            >
              <RequestIcon />
            </div>
          )}
        </div>
      </div>
      <div className='flex'>
        <div className='text-10 font-bold flex-center rounded-lg p-sm h-[22px] uppercase bg-tealTierBg text-tealTier' style={{ letterSpacing: '0.1em' }}>
          {getTier(offer?.lowest_visible_tier)}
        </div>
      </div>
      <div className="flex items-center text-18 ">
        <img className='mr-sm' src="/svg/golf-post-small.svg" />
        {club?.name}
      </div>

      <div className='text-gray flex mb-sm'>
        <div className='pt-[2px] mr-xs'>
          <img src="/svg/location-grey.svg" style={{ minWidth: 14, maxWidth: 14, minHeight: 14, maxHeight: 14 }} />
        </div>
        {club?.addr}
      </div>

      <div className='flex flex-col text-grayLight mb-sm'>
        <div className='text-12'>Created by - {offer?.creatorName}</div>
      </div>

      <div className=''>
        {offer?.details}
      </div>

    </div>

  )
}

export default OfferListItem

