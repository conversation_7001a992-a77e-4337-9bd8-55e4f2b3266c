import React, { useContext, useState } from 'react'
import determineRequestTab from '../../utils/requests/determineRequestTab'
import router from 'next/router'
import { findOfferExistsByOfferId } from '../../utils/offers/findOfferExists'
import constantOptions from '../../constants/constantOptions'
import { UserContext } from '../../pages/_app'
import moment from 'moment'
import getInitials from '../../utils/helper/getInitials'
import NameInitials from '../common/NameInitials'
import { ModalContext } from '../../context/ModalContext'

export default function NotificationGroupCard({ notification, groupedNotificationIds, closeDrawer, client, fetchNotifications, currentPageNotifCount, deleteNotification, groupLength }) {

    const { FRIENDS_TAB_OPTIONS, MY_TG_HEADER } = constantOptions
    const { user, token, fetchUser } = useContext(UserContext)
    const { setModal } = useContext(ModalContext)
    const [isHovered, setIsHovered] = useState(false)

    const notificationImg =
        notification?.type === 'bookmark-reply'
            ? '/svg/save.svg'
            : notification?.type === 'my-post-comment'
                ? '/svg/comment.svg'
                : ['host-request', 'my-host-request', 'host-game-completed', 'confirmed-request', 'game-completed', 'admin-updated-game-status'].includes(notification?.type)
                    ? '/svg/Notifications/Request.svg' :
                    notification?.type === 'cancelled-request' ?
                        "/svg/Notifications/Cancelled.svg"
                        : notification?.type === 'favorite-club-offer' || notification?.type === 'my-tg-offer' || notification?.type === 'my-tg-offer-request'
                            ? '/svg/Notifications/Offer.svg'
                            : notification?.type === 'request-chat'
                                ? '/svg/chat2.svg'
                                : notification?.type === 'request-accepted'
                                    ? '/svg/profile-play-active.svg'
                                    : notification?.type === 'event'
                                        ? '/svg/Notifications/Event.svg'
                                        : notification?.type === 'benefit'
                                            ? '/svg/Notifications/Benefit.svg'
                                            : notification?.type === 'account-activated'
                                                ? '/svg/Notifications/GolfSticks.svg'
                                                : notification?.type === 'admin-profile-edit'
                                                    ? '/images/edit.svg'
                                                    : '/svg/forum.svg'

    async function redirectUser({ type, data }) {
        // @TODO LATER - start the loader
        // setLoading(true);
        closeDrawer();

        let link = "/";  // By default redirecting to the home page
        let search = "";
        if (['game-completed', 'cancelled-request', 'declined-request', 'confirmed-request', 'host-request', 'my-host-request', 'game', 'requester-game-completed', 'host-game-completed', 'admin-updated-game-status'].includes(type)) {
            link = "/dashboard/play";

            if (data?.request_id) {
                const response = await determineRequestTab({ request_id: type === 'offline-game-logged' ? data?.groupId : data?.request_id, user_id: user?.id, data: false });
                if (response?.code !== 200) {
                    link = "/dashboard/errors/404";
                } else {
                    let res = response?.tab.split("/");
                    link = `/dashboard/play`
                    search = `?type=requests&tab=${res[0]}&subtype=${res[1]}&id=${data?.request_id}`
                }
            }
        } else if (type === 'event') {
            link = "/dashboard/events";
            if (data?.event_id) {
                try {
                    const { event_by_pk } = await client.request(`
                        {
                            event_by_pk(id: "${data?.event_id}") {
                            id
                            }
                        }                  
                    `);
                    if (event_by_pk?.id) {
                        link = `/dashboard/events`
                        search = `type=event&id=${data?.event_id}`
                    } else {
                        link = "/dashboard/errors/404";
                    }
                } catch (error) {
                    link = "/dashboard/errors/404";
                }
            }
        }
        else if (type === 'benefit') {
            link = "/dashboard/benefits";
            if (data?.benefit_id) {
                try {
                    const { benefit_by_pk } = await client.request(`
                        {
                            benefit_by_pk(id: "${data?.benefit_id}") {
                            id
                            }
                        }                  
                    `);

                    if (benefit_by_pk?.id) {
                        link = `/dashboard/benefits`
                        search = `type=benefit&id=${benefit_by_pk?.id}`
                    } else {
                        link = "/dashboard/errors/404";
                    }
                } catch (error) {
                    link = "/dashboard/errors/404";
                }
            }
        }
        else if (type === 'favorite-club-offer') {
            link = `/dashboard/play`
            search = `type=offers`

            if (data?.offer_id) {
                const response = await findOfferExistsByOfferId(data?.offer_id);

                if (!response) {
                    link = "/dashboard/errors/404";
                }
            }
        } else if (type === "friend-request-accepted") {
            link = `/dashboard/my-tg`
            search = `?type=${MY_TG_HEADER.MY_FRIENDS_LOWERCASE}&subtype=${FRIENDS_TAB_OPTIONS.ALL_FRIENDS}`

        } else if (type === "friend-request-declined") {
            link = `/dashboard/my-tg`
            search = `?type=${MY_TG_HEADER.MY_FRIENDS_LOWERCASE}&subtype=${FRIENDS_TAB_OPTIONS.SENT}`
        }
        else if (['request-chat', 'request-chat-new-message'].includes(type)) {
            if (data?.request_id) {
                const response = await determineRequestTab({ request_id: data?.request_id, user_id: user?.id, data: false });

                if (response?.code !== 200) {
                    link = "/dashboard/errors/404";
                } else {
                    link = `/dashboard/request-chat/${data?.request_id}`;
                    if (data?.host_id) {
                        link += `/${data?.host_id}`
                    }
                }
            }
        } else if (['admin-profile-edit', 'admin-clubs-edit', 'account-activated', 'my-thousand-greens', 'tier-revision', 'update-email', 'tga-friend-connected'].includes(type)) {
            link = '/profile/profile-settings';
        } else if (type === 'admin-clubs-edit') {
            link = '/profile/golf-clubs'
        } else if (type === 'chat-invite') {
            link = '/dashboard/my-tg'
            search = '?type=chat'
        } else if (type === 'friend-request-received') {
            link = `/dashboard/my-tg`
            search = `?type=${MY_TG_HEADER.MY_FRIENDS_LOWERCASE}&subtype=${FRIENDS_TAB_OPTIONS.RECEIVED}`
        } else if (type === "join-group-request") {
            link = '/dashboard/my-tg'
            search = `?type=pending-requests&groupId=${data?.groupId}`
        } else if (type === "my-tg-groups") {
            link = '/dashboard/my-tg'
            search = `?type=groups`
        } else if (type === 'faq') {
            link = '/dashboard/help'
        } else if (type === 'my-tg-offer') {
            link = '/dashboard/offers'
            search = `?offerId=${data?.offer_id}&clubId=${data?.club_id}`
        } else if (type === 'referral-joined') {
            link = '/profile/tg-referrals'
        } else if (type === 'club-update') {
            link = '/profile/golf-clubs'
        } else if (type === 'muted-club-reminder') {
            setModal({
                type: 'unmute-club-reminder',
                user,
                fetchUser
            })
            return
        } else if (type === 'game-reminder') {
            link = "/dashboard/play";
            const response = await determineRequestTab({ request_id: data?.request_id, user_id: user?.id, data: false });
            if (response?.code !== 200) {
                link = "/dashboard/errors/404";
            } else {
                let res = response?.tab.split("/");
                link = `/dashboard/play`
                search = `?type=requests&tab=${res[0]}&subtype=${res[1]}&id=${data?.request_id}`
            }
        } else if (type === 'pegboard') {
            link = '/dashboard/play'
            search = `?type=pegboards&id=${data?.pegboard_id}`
        } else if (type === 'new-club-user') {
            link = '/profile/golf-clubs',
                search = `?clubId=${data?.clubId}`
        } else if (type === 'membership') {
            link = '/profile/membership'
        } else if (type === 'ngv-update') {
            link = '/dashboard/membership-logs'
        } else if (['unmute-user-club', 'female-member-joined', 'couple-joined',].includes(type)) {
            link = '/dashboard/map'
            search = `?clubId=${data?.clubId}`
        } else if (type === 'my-tg-group') {
            link = '/dashboard/my-tg'
            search = `?type=groups&id=${data?.streamChannelId}`
        } else if (type === 'offline-game-logged') {
            link = '/dashboard/membership-logs'
        }

        if (link !== "/") {
            router.push({
                pathname: link,
                search
            })

        }

    }

    const formatTimestampDifference = (timestamp) => {
        const now = moment();
        const givenTime = moment(timestamp);
        const diffInSeconds = now.diff(givenTime, "seconds");

        if (diffInSeconds < 60) {
            return "Just Now";
        } else if (diffInSeconds < 3600) {
            const minutes = now.diff(givenTime, "minutes");
            return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = now.diff(givenTime, "hours");
            return `${hours} hour${hours > 1 ? "s" : ""} ago`;
        } else {
            return givenTime.format("MM/DD/YYYY");
        }
    }

    function renderNotification({ type, data, groupLength }) {
        if (type === 'bookmark-reply' || type === 'my-post-comment')
            return (
                <div className="w-full">
                    {notification?.html_message ? (
                        <div dangerouslySetInnerHTML={{ __html: notification?.html_message }} />
                    ) : (
                        <div>{notification?.message} {groupLength > 1 && `(${groupLength})`}</div>
                    )}
                    {notification?.data?.headline && (
                        <div
                            onClick={closeDrawer}
                            style={{ textOverflow: 'ellipsis' }}
                            className="overflow-hidden font-normal whitespace-no-wrap cursor-pointer text-darkteal hover:underline pr-sm">
                            {notification?.data.headline}
                        </div>
                    )}
                    <div className='text-12 text-gray pt-sm'>{formatTimestampDifference(notification?.created_at)}</div>
                </div>
            )

        return (
            <div className="w-full">
                {notification?.html_message ? (
                    <div
                        onClick={() => {
                            redirectUser({ type, data });
                        }}
                        style={{ textOverflow: 'ellipsis' }}
                        className={`cursor-pointer hover:underline pr-sm`}
                        dangerouslySetInnerHTML={{ __html: notification?.html_message }}
                    />
                ) : (
                    <div
                        onClick={() => {
                            redirectUser({ type, data });
                        }}
                        style={{ textOverflow: 'ellipsis' }}
                        className={`cursor-pointer hover:underline pr-sm`}>
                        {notification?.message} {groupLength > 1 && `(${groupLength})`}
                    </div>
                )}
                <div className='text-12 text-gray pt-sm'>{formatTimestampDifference(notification?.created_at)}</div>
            </div>
        )
    }

    if (!notification) {
        return null;
    }

    if (notification?.more && currentPageNotifCount === constantOptions.NOTIFICATION_LIMIT) {
        return (
            <p className="text-center text-darkteal cursor-pointer py-md hover:bg-offwhite" onClick={() => fetchNotifications()}>
                Load More
            </p>
        );
    }

    if (notification?.type === 'bookmark-reply' || notification?.type === 'my-post-comment') {
        return null;
    }

    return (
        <div
            style={{ minHeight: 64 }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className="hover:bg-tealTierBg relative bg-white px-md text-sm rounded-lg flex py-sm flex-center w-full border-b border-lightestgray">
            {isHovered && (
                <img onClick={() => {
                    deleteNotification(groupedNotificationIds ? groupedNotificationIds : [notification?.id], user?.id)
                }} width={10} className='absolute right-[10px] top-[10px] cursor-pointer' src={"/svg/CrossIconBlack.svg"} />
            )}
            <div className="h-full">
                {['admin-clubs-edit', 'admin-profile-edit'].includes(notification?.type) ? (
                    <div className='mr-md'>
                        <NameInitials
                            key="ni2"
                            user={user}
                            height={32}
                            width={32}
                            fontSize={16}
                            role={'admin'}
                            background='bg-tealTierBg'
                            rounded='full'
                        />
                    </div>
                ) : (notification?.data?.userPhoto || notification?.data?.groupImage) ? (
                    <div
                        className='rounded-full mr-md flex-center h-[32px] w-[32px] relative'
                        style={{
                            backgroundImage: `url("${notification?.data?.userPhoto || notification?.data?.groupImage}")`,
                            backgroundPosition: 'center',
                            backgroundSize: 'cover',
                        }}>
                        {!notification?.read && (
                            <div className='h-[6px] w-[6px] bg-darkteal rounded-full absolute left-[5px]' />
                        )}
                    </div>
                ) : (notification?.data?.userName || notification?.data?.groupName) ? (
                    <div className='rounded-full bg-tealTierBg mr-md flex-center h-[32px] w-[32px] font-medium text-20 text-darkteal'>
                        {getInitials(notification?.data?.userName || notification?.data?.groupName)}
                    </div>
                ) : (
                    <div className="rounded-full bg-tealTierBg mr-md flex-center h-[32px] w-[32px]">
                        {!notification?.read && (
                            <div className='min-h-[6px] min-w-[6px] bg-darkteal rounded-full absolute left-[5px]' />
                        )}
                        <img
                            src={notificationImg}
                            width={16}
                            alt=""
                        />
                    </div>
                )}
            </div>
            <div className="font-normal text-gray w-full" style={{ fontSize: 14 }}>
                {renderNotification({ type: notification?.type, data: notification?.data, groupLength })}
            </div>
        </div>
    );
}
