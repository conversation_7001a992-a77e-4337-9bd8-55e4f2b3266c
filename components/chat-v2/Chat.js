import React, { useContext, useMemo, useState } from 'react';
import { UserContext } from '../../pages/_app'
import dynamic from 'next/dynamic';
import {
    Chat,
    Channel,
    ChannelList,
    Thread,
    DefaultSuggestionList,
} from 'stream-chat-react';
import { useEffect } from 'react';
import TypingIndicator from './ChannelWindow/TypingIndicator';
import EmptyMessageList from './ChannelWindow/EmptyMessageList';
import ChannelPaginator from '../../components/chat-v2/ChannelPreview/ChannelPaginator';
import EmptyListPlaceholder from '../../components/chat-v2/common/EmptyListPlaceholder';
import CustomResultItem from '../../components/chat-v2/ChannelListHeader/CustomResultItem';
import PulseSkeletonV2 from '../../components/common/PulseSkeletonV2';
import NoSearchResults from '../../components/chat-v2/common/NoSearchResults';
import customChannelSearchWithDelay from '../../utils/stream/customChannelSearchWithDelay';
import streamOptions from "../../constants/streamOptions";
import ChannelListHeader from '../../components/chat-v2/ChannelListHeader/ChannelListHeader';
import ChannelPreview from '../../components/chat-v2/ChannelPreview/ChannelPreview';
import CustomChannelList from '../../components/chat-v2/ChannelPreview/CustomChannelList';
import { CustomStreamContext } from '../../context/CustomStreamContext';
import NewChatFriendList from '../../components/chat-v2/NewChat/NewChatFriendList';
import NewGroupList from '../../components/chat-v2/Group/NewGroupList';
import BlockedList from '../../components/chat-v2/Blocked/BlockedList';
import moment from 'moment';
import { useRouter } from 'next/router';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';
import { DrawerContext } from '../../context/DrawerContext';
import { EmojiPicker } from 'stream-chat-react/emojis';
import CustomMessageLoadIndicator from '../common/CustomMessageLoadIndicator';
import SystemMessage from './ChannelWindow/SystemMessage';

const ChannelWindow = dynamic(() => import('../../components/chat-v2/ChannelWindow/ChannelWindow'))
const DateSeparator = dynamic(() => import('../../components/chat-v2/ChannelWindow/DateSeparator'))
const NoConversationSelected = dynamic(() => import('../../components/chat-v2/ChannelPreview/NoConversationSelected'));
const { ONE_TO_ONE, SYSTEM_THOUSAND_GREENS_PUBLIC, SYSTEM_PRIVATE_NETWORK, MY_TG_GROUP, REQUEST_CHAT_GROUP } = streamOptions?.CHANNEL?.TYPES
const { CHANNEL: { SORT, STANDARD_FILTERS, OPTIONS }, AVATAR_CHANNEL_NAME_HEIGHT } = streamOptions;
const COMPONENT = streamOptions?.CHAT_LIST_CONSTANTS

const TgChat = () => {

    const takeValuesFromSession = sessionStorage.getItem('urlHistory') ?
        JSON.parse(sessionStorage.getItem('urlHistory'))?.previous?.includes('/dashboard/events') :
        null

    const { user, streamChatClient, token } = useContext(UserContext)
    const { drawer, setDrawer } = useContext(DrawerContext);
    const {
        showDetails,
        getMembersOfChannel,
        newChatStates: { currentComponent },
        updateNewChatComponent,
        showArchiveList,
        setshowArchiveList,
        updateBlockStateOfChannel,
        updateStreamDeletedUsers,
        channelSelected,
        setChannelSelected,
        setShowOfferAndEventDetails,
        setShowDetails,
        myFriendsId
    } = useContext(CustomStreamContext)

    const [filters, setFilters] = useState(STANDARD_FILTERS(user?.id, moment()));
    const [reRenderForcefully, setReRenderForcefully] = useState(0); // This state is for re-rendering the custom channel list when a message comes on a 1:1 channel (to show it on the channel list)
    const [customActiveChannelId, setCustomActiveChannelId] = useState(takeValuesFromSession ? window?.sessionStorage.getItem("MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL") : '')
    const [query, setQuery] = useState('')
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const router = useRouter()
    const mobileDetails = isMobile && channelSelected && drawer?.type
    const mobileNoDetails = isMobile && channelSelected && !drawer?.type
    const tabletDetails = isTablet && drawer?.type
    const tabletNoDetails = isTablet && !drawer?.type
    const [selectedChatType, setSelectedChatType] = useState(streamOptions?.CHAT_TYPES[0]?.value)


    if (!streamChatClient) {
        return null
    }

    useEffect(() => {
        setFilters(STANDARD_FILTERS(user?.id, moment(), false, selectedChatType))
        setReRenderForcefully((prev) => prev + 1)
    }, [selectedChatType])

    const queryChannel = async (channelId) => {
        const [channel] = await streamChatClient.queryChannels({
            id: { $eq: channelId },
            $or: [{ hidden: { $eq: true } }, { hidden: { $eq: false } }]
        })

        setChannelSelected(true)
        if (channel?.data.hidden) {
            setFilters(STANDARD_FILTERS(user?.id, moment(), true))
            setshowArchiveList(true)
            setshowArchiveList(false)
        }
    }

    useEffect(() => {
        // Setting the drawer as null so that the group details from my tg section closes
        // setDrawer();

        if (router.query?.channel_id) {
            setCustomActiveChannelId(router.query?.channel_id)
            queryChannel(router.query?.channel_id)
            // router.replace({ pathname: router?.pathname, query: '' }, undefined, {
            //     shallow: false,
            // })
        }
    }, [router.query?.channel_id])

    useEffect(() => {
        // If the last URL was events, and there is a value for MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL in session, then we need to show Offer-Events Panel
        if (takeValuesFromSession && window?.sessionStorage.getItem("MY_TG_GROUP_CHANNEL_ID_OFFER_EVENT_PANEL")) {
            setShowOfferAndEventDetails(true)
            setShowDetails(true)
        }
    }, [takeValuesFromSession])

    //Listen to all events happening on stream
    useEffect(() => {
        const channelUpdateEvent = streamChatClient?.on(
            'channel.updated',
            (event) => {
                if (event?.channel?.type === streamOptions.CHANNEL.TYPES.ONE_TO_ONE) {
                    updateBlockStateOfChannel(event?.channel)
                }
            }
        )

        const userDeletedEvent = streamChatClient?.on('user_deleted', (event) => {
            updateStreamDeletedUsers(event?.user?.id)
        })

        return () => {
            channelUpdateEvent?.unsubscribe()
            userDeletedEvent?.unsubscribe()
        }
    }, [])

    // We are doing this so that we can refresh when the user leaves or joins PN or the admin disables the PN group
    useEffect(() => {
        setReRenderForcefully(prev => prev + 1);
    }, [user?.private_network, user?.private_network?.createChatGroup])

    useEffect(() => {
        const abortController = new AbortController()
        updateNewChatComponent(COMPONENT?.MAIN_STREAM_CHANNEL_LIST);

        return () => {
            // We are setting the showArchiveList state to false because we want to 
            // always open the recent chat listing when we come on the chat page
            setshowArchiveList(false);
            abortController.abort()
        }
    }, [])


    useEffect(() => {
        // we are setting the filters everytime we land on the main channel list component because there
        // were some cases when the channels did not come when we messaged any user from another component
        if (currentComponent === COMPONENT?.MAIN_STREAM_CHANNEL_LIST) {
            setFilters(STANDARD_FILTERS(user?.id, moment()))
        }
    }, [currentComponent])

    useEffect(() => {
        if (showArchiveList !== undefined) {
            setFilters(STANDARD_FILTERS(user?.id, moment(), showArchiveList))
            setReRenderForcefully((prev) => prev + 1)
        }
    }, [showArchiveList])


    const customChannelRenderFunction = (channels) => {
        return channels.filter(channel => {
            if (channel?.data?.type === REQUEST_CHAT_GROUP) {
                return false;
            }
            if (channel?.data?.disabled) {
                return false;
            }

            if (channel?.type === ONE_TO_ONE) {
                if (channel?.state?.messages?.length) {
                    // For archive channels list
                    if (showArchiveList) {
                        return channel?.data?.hidden;
                    }
                    return true;
                }
                return false;
            }

            // If user is leaves TG and became PN only member then do not show the TG PUBLIC chat group,
            // We are not showing the channel from frontend but eventually the user will be removed from the chat group.
            if (
                !user?.visibleToPublic &&
                channel?.data?.type === SYSTEM_THOUSAND_GREENS_PUBLIC
            ) {
                return false
            }

            // When user leave from a Private network then, do not show the pn chat group.
            // We are not showing the channel from frontend but eventually the user will be removed from the chat group.
            if (
                !user?.private_network?.id &&
                channel?.data?.type === SYSTEM_PRIVATE_NETWORK
            ) {
                return false
            }

            // For archive channels list
            if (showArchiveList) {
                return channel?.data?.hidden;
            }

            return true;
        })
    }

    const channelSearch = async ({ setResults, setSearching, setQuery }, value) => {
        // We will set the query and searching on the spot as we have to enable and start the search
        setQuery(value);
        setSearching(true);

        // We will call the search function with debounce so that we can control the network calls
        await customChannelSearchWithDelay({ setResults, setSearching }, value, user?.id, streamChatClient, selectedChatType)
    }

    // we are using use memo because otherwise the component re-renders everytime there is any change in any other state
    // which causes the chat listing to scroll up whenever any channel is selected from the list
    const computedChannelList = useMemo(() => {
        return (
            <ChannelList
                sendChannelsToList={true}
                channelRenderFilterFn={(channels) => customChannelRenderFunction(channels)}
                setActiveChannelOnMount={false}
                customActiveChannel={customActiveChannelId}
                Preview={
                    (props) => <ChannelPreview
                        {...props}
                        user={user}
                        streamChatClient={streamChatClient}
                    />
                }

                List={(props) => <CustomChannelList
                    {...props}
                    setReRenderForcefully={setReRenderForcefully}
                    user={user}
                    token={token}
                    showArchiveList={showArchiveList}
                />}
                showChannelSearch={true}
                additionalChannelSearchProps={{
                    searchFunction: (params, event) => channelSearch(params, event?.target?.value),
                    clearSearchOnClickOutside: false,
                    searchForChannels: true,
                    searchQueryParams: { channelFilters: { filters, SORT, OPTIONS } },
                    SearchInputIcon: () => <></>,
                    ClearInputIcon: () => <></>,
                    MenuIcon: () => <></>,
                    ExitSearchIcon: () => <></>,
                    SearchResultsHeader: () => <></>,
                    SearchInput: (props) => <ChannelListHeader
                        {...props}
                        user={user}
                        setQuery={setQuery}
                        selectedChatType={selectedChatType}
                        setSelectedChatType={setSelectedChatType}
                    />,
                    SearchResultItem: (props) => <CustomResultItem {...props} />,
                    SearchEmpty: () => <div className="flex" style={{ height: "calc(100vh - 172px)" }}><NoSearchResults /></div>,
                    SearchLoading: () => <PulseSkeletonV2 times={5} height={AVATAR_CHANNEL_NAME_HEIGHT} />,
                }}
                filters={filters}
                sort={SORT}
                options={OPTIONS}
                Paginator={(props) => <ChannelPaginator {...props} />}
                EmptyStateIndicator={() => (
                    <div className="flex gap-6 flex-wrap justify-center items-center" style={{ height: "calc(100vh - 127px)" }}>
                        {!showArchiveList ? (
                            <EmptyListPlaceholder
                                text={selectedChatType === "group" ? "You're not part of any TG groups yet!" : "You don't have any chats yet"}
                                buttonText="Start a New Chat"
                                buttonClickHandler={() =>
                                    updateNewChatComponent(0)
                                }
                            />
                        ) : (
                            <EmptyListPlaceholder text="You haven't archived any chat yet" />
                        )}
                    </div>
                )}
            />
        )
    }, [reRenderForcefully, selectedChatType])

    const leftChatMenu = () => {
        switch (currentComponent) {
            case COMPONENT?.MAIN_STREAM_CHANNEL_LIST:
                return computedChannelList
            case COMPONENT?.NEW_1_TO_1_CHAT:
                return (<NewChatFriendList />)
            case COMPONENT?.NEW_GROUP:
                return (<NewGroupList />)
            case COMPONENT?.NEW_GROUP_FORM:
                return (<NewGroupList />)
            case COMPONENT?.BLOCKED:
                return (<BlockedList userId={user?.id} token={token} />)
            default:
                break;
        }
    }

    const renderMentions = (props) => {
        const friendIds = myFriendsId
        const mentions = props?.values

        const modifiedMentions = mentions.map(user => {
            const userId = user.id;
            const isIdPresent = friendIds[userId];

            return {
                ...user,
                name: !isIdPresent ? user.username : user.name,
                first_name: !isIdPresent ? user.username : user.first_name,
                last_name: !isIdPresent ? "" : user.last_name,
            };
        });
        return <DefaultSuggestionList {...props} values={modifiedMentions} />
    }

    const channelWindow = () => {
        return (
            <Channel
                MessageSystem={SystemMessage}
                QuotedMessage={() => <></>}
                DateSeparator={DateSeparator}
                TypingIndicator={TypingIndicator}
                EmptyStateIndicator={EmptyMessageList}
                LoadingIndicator={() => <CustomMessageLoadIndicator />}
                multipleUploads={true}
                maxNumberOfFiles={streamOptions?.MAX_FILES_ALLOWED_TO_UPLOAD}
                EmptyPlaceholder={!isMobile && <NoConversationSelected />}
                AutocompleteSuggestionList={(props) => {
                    if (props?.currentTrigger === '@') {
                        return renderMentions(props)
                    } else {
                        return <DefaultSuggestionList {...props} />
                    }

                }}
                EmojiPicker={EmojiPicker}
            >
                <ChannelWindow setReRenderForcefully={setReRenderForcefully} />
                <Thread />
            </Channel>
        )
    }

    return (
        <div className={`flex h-full border-t border-lightestgray custom-chat-screen-layout ${(query && query !== '') && 'channel-search-list'}`}>
            <Chat client={streamChatClient} theme='str-chat__theme-light w-[]' useImageFlagEmojisOnWindows={true} >
                {/* Condition for showing Left Side Channel List */}
                {(!isMobile || (isMobile && !channelSelected)) &&
                    <>
                        {leftChatMenu()}
                    </>
                }

                {(isDesktop || isWideScreen) ?
                    channelWindow()
                    :
                    ((isMobile && channelSelected) || isTablet) ? (
                        <div className={`${mobileDetails ? 'mobile-screen-details w-full' : mobileNoDetails ? 'mobile-screen-no-details w-full' : tabletDetails ? 'tablet-screen-details' : tabletNoDetails ? 'tablet-screen-no-details' : ''}`}>
                            {channelWindow()}
                        </div>
                    ) : null
                }
            </Chat >
        </div>
    )
}

export default React.memo(TgChat);