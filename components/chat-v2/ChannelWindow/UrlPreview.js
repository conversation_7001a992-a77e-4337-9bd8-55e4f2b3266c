import React from 'react'

const UrlPreview = ({ attachmentData, isSelfMessage }) => {

    return (
        <div className={`flex ${isSelfMessage ? 'bg-darkestteal' : 'bg-lightestgray'} cust-link-header rounded-lg`}>
            <div className='px-sm py-sm'>
                <div>{attachmentData?.title}</div>
                <div className='text-12'>{attachmentData?.text}</div>
            </div>

            <div className='rounded-r-lg'
                style={{
                    height: "100%",
                    width: "100px",
                    backgroundImage: `url(${attachmentData?.image_url })`,
                    backgroundPosition: 'center',
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat'
                }}
            >
            </div>
        </div>
    )
}

export default UrlPreview