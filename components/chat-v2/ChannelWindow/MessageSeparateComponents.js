import moment from "moment";
import { useContext } from "react";
import { UserContext } from "../../../pages/_app";
import { Attachment, MessageText, MessageTimestamp } from "stream-chat-react";
import streamOptions from "../../../constants/streamOptions";
import { ChatGalleryViewModalContext } from "../../modals/ChatGalleryViewModal";
import UrlPreview from "./UrlPreview";
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { CustomStreamContext } from "../../../context/CustomStreamContext";
import { DrawerContext } from "../../../context/DrawerContext";


const { DELETED_MESSAGE_TEXT } = streamOptions;

const DeletedMessage = ({ isMessageDeleted, extraClasses = '' }) => {
  if (isMessageDeleted) {
    return (
      <div className={extraClasses}>{DELETED_MESSAGE_TEXT}</div>
    )
  }
  return ''
}

const TextMessage = ({ hasTextMessage, message, mentionedUsers }) => {
  const { setShowDetails, setShowProfileDetail, setOtherUserId, setShowBackOption } = useContext(CustomStreamContext)
  const { user: { id: userId } } = useContext(UserContext)
  const { drawer, setDrawer } = useContext(DrawerContext)

  /**
   *
   * @param {*} messageData
   * @returns
   */

  const mentionUsers = (messageData) => {
    /** Handle click on mentioned user name
     *
     * @param {*} mentioned_users
     * @param {*} data
     */

    const handleMentionedName = (mentioned_users, data) => {
      // Check selected name with all mentioned user array and navigate to a particular user's profile
      mentioned_users?.map((user) => {
        if (userId !== user?.id) {
          if (user?.username === data || user?.name === data) {
            setOtherUserId(user?.id),
              setDrawer({
                type: "profile",
                source: "chat",
                channelId: user?.id
              })
          }
        }
      });
    };

    let temp = [];
    messageData?.mentioned_users?.map((data) => {
      temp.push(data?.username);
      temp.push(data?.name);
    });

    let test = [];
    let text = messageData?.text;
    temp.forEach((data, index) => {
      if (text?.includes(`@${data}`)) {
        let matchFound = false

        if (text.indexOf(`@${data}`) === 0) {
          test.push(
            <span
              className="mentioned"
              key={messageData?.id || index}
              onClick={() =>
                handleMentionedName(
                  messageData?.mentioned_users,
                  data,
                )
              }>
              {messageData?.mentioned_users?.map((user) => {
                if (!matchFound) {
                  if (
                    user?.name === data ||
                    user?.username === data
                  ) {
                    matchFound = true
                    if (user?.deleted_at) {
                      return (
                        <span key={user?.id} className="deleted-mentioned">
                          Deleted User{' '}
                        </span>
                      );
                    } else {
                      return <>{`@${data}`} </>;
                    }
                  }
                }
              })}
            </span>,
          );

          let newText = text.replace(`@${data}`, '');
          text = newText.trim();
        } else {
          test.push(
            <span key={messageData?.id || index}>
              {text.slice(0, text.indexOf(`@${data}`))}{' '}
            </span>,
          );
          test.push(
            <span
              key={messageData?.id || index}
              className="mentioned"
              onClick={() =>
                handleMentionedName(
                  messageData?.mentioned_users,
                  data,
                )
              }>
              {messageData?.mentioned_users?.map((user, _i) => {
                if (
                  user?.name === data ||
                  user?.username === data
                ) {
                  if (user?.deleted_at) {
                    return (
                      <span className="deleted-mentioned" key={index}>
                        Deleted User{' '}
                      </span>
                    );
                  } else {
                    return <span className="mentioned"
                      key={index}
                      onClick={() =>
                        handleMentionedName(
                          messageData?.mentioned_users,
                          data,
                        )
                      }
                    >{`@${data}`} </span>;
                  }
                }
              })}
            </span>,
          );

          let newText = text.slice(0, text.indexOf(`@${data}`));
          let newText1 = text.replace(newText, '');
          let newText2 = newText1.replace(`@${data}`, '');
          text = newText2.trim();
        }

        if (index === temp.length - 1) {
          test.push(
            <span
              key={index}
            >
              {text}{' '}
            </span>,
          );
        }
      } else {
        if (index === temp.length - 1) {
          test.push(
            <span
              key={index}
            >
              {text}{' '}
            </span>,
          );
        }
      }
    });

    return (
      <div>
        {test?.map((data) => {
          return data;
        })}
      </div>
    );

  };

  if (hasTextMessage) {
    return (
      <div
        className={`flex pl-sm p-3px`}>
        {mentionedUsers?.length ? (
          mentionUsers(message)
        ) : (
          <MessageText message={{ ...message, text: `${message?.text?.trim()}` }} />
        )}
        <span className="text-10 invisible">{moment(message?.created_at).format('LT')}</span>
      </div>
    )
  }
  return ''
}

const CustomImage = (props) => {
  const { setChatImageModal } = useContext(ChatGalleryViewModalContext);

  const original_width = props.original_width < 50 ? 300 : props.original_width
  // This part is setting image min width based on the original width and original height we are getting
  const imageMinWidth = `${(original_width / (props.original_height / Math.min(350, Math.min(350 / original_width, 1) * props.original_height)))}px`

  return (
    <>
      <LazyLoadImage
        onClick={() => {
          setChatImageModal({
            images: [{image_url: props.image_url, fallback: props?.fallback, type: "image"}],
            fallback: props.fallback,
            imageIndex: 0,
          });
        }}
        src={props.image_url}
        width={imageMinWidth}
        alt="Image Alt"
        className="custom-chat-single-image str-chat__gallery-image"
        style={{ cursor: 'default' }}
        placeholderSrc='url(/images/placeholder.jpg)'
        threshold={160}
      />
    </>
  )
  // render custom image component here
};

const CustomGallery = (props) => {
  const { setChatImageModal } = useContext(ChatGalleryViewModalContext);
  return (
    <>

      <div style={{ width: '350px', minWidth: '350px', maxWidth: '350px' }} className={`str-chat__gallery ${props.images.length == 2 ? '' : 'str-chat__gallery--square str-chat__gallery-two-rows'}`}>
        {
          props.images.map((image, index) => {
            return index === 3 ? (
              <div key={image.image_url} className="relative img-container">
                <LazyLoadImage
                  onClick={() => {
                    setChatImageModal({
                      images: props.images,
                      imageIndex: index,
                      fallback: image.fallback,
                    });
                  }}
                  src={image.image_url}
                  alt="Image Alt"
                  className="str-chat__gallery-image cursor-pointer"
                  placeholderSrc='url(/images/placeholder.jpg)'
                  threshold={160}
                />
                <p className="text-white absolute bg-lightergray opacity-70 px-2 rounded-7px cursor-pointer"
                  style={{ zIndex: 1000, top: 70, left: 50, fontSize: 25 }}>
                  {props.images.length > 4 ? `${props.images.length - 4} more` : ''}
                </p>
              </div>
            ) : (
              <LazyLoadImage
                onClick={() => {
                  setChatImageModal({
                    images: props.images,
                    imageIndex: index,
                    fallback: image.fallback,
                  });
                }}
                key={image.image_url}
                src={image.image_url}
                alt="Image Alt"
                className="str-chat__gallery-image cursor-pointer"
                placeholderSrc='url(/images/placeholder.jpg)'
                threshold={160}
              />
            )
          })
        }
      </div>
    </>
  )
  // render custom image component here
};

const AttachmentOrUrlWrapper = ({ hasAttachments, messageHasUrl, message, isSelfMessage }) => {

  if (hasAttachments) {
    return (
      <>
        {
          messageHasUrl ? (
            <UrlPreview
              attachmentData={message?.attachments[0]}
              isSelfMessage={isSelfMessage}
            />
          ) : (
            <div id='other-file-attachment'>
              <Attachment attachments={message?.attachments} Image={CustomImage} Gallery={CustomGallery} />
            </div>
          )}
      </>

    )
  }
  return ''
}

const MessageTimestampWrapper = ({ hasAttachments, messageHasUrl, message }) => {
  return (
    <div
      className={`message-timestamp text-10 ${(hasAttachments || messageHasUrl) ? 'absolute' : 'flex justify-end'}`}
    >
      <MessageTimestamp message={message} />
    </div>
  )
}

export { DeletedMessage, TextMessage, AttachmentOrUrlWrapper, MessageTimestampWrapper };