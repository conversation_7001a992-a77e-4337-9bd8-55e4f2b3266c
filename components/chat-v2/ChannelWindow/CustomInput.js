import React, { useContext, useEffect, useRef, useState } from 'react'
import QuotedMessagePreview from './QuotedMessagePreview';
import {
    AttachmentPreviewList,
    ChatAutoComplete,
    useChannelStateContext,
    useMessageInputContext,
    useChannelActionContext,
} from 'stream-chat-react';
import AttachmentIcon from '../common/AttachmentIcon';
import OptionsButton from '../common/OptionsButton';
import streamOptions from '../../../constants/streamOptions';
import DisabledMessageInput from '../common/DisabledMessageInput';
import { CustomStreamContext } from '../../../context/CustomStreamContext';
import { get1to1ChannelStatus } from '../../../utils/stream/get1to1ChannelStatus';
import compressImage from '../../../utils/helper/compressImage';
import data from '@emoji-mart/data'
import Picker from '@emoji-mart/react'
import useClient from '../../../graphql/useClient';
import { ModalContext } from '../../../context/ModalContext';
import getChannelName from '../../../utils/stream/getChannelName';
import { UserContext } from '../../../pages/_app';
import ENDPOINTS from "../../../constants/endpoints.json"
import { forEach } from 'lodash';

const {
    ACCEPTED_FORMATS: { PHOTO_VIDEO, DOCUMENTS },
    MAX_IMAGE_SIZE: { SIZE_IN_MB, ERROR }
} = streamOptions
const { MY_TG_GROUP, SYSTEM_THOUSAND_GREENS_PUBLIC, ONE_TO_ONE, USER_CREATED_GROUP, REQUEST_CHAT_GROUP } = streamOptions?.CHANNEL?.TYPES;


const CustomInput = () => {
    const { channel, quotedMessage, maxNumberOfFiles, multipleUploads } = useChannelStateContext();
    const { addNotification } = useChannelActionContext()
    const attachmentUploadInput = useRef(null);
    const [acceptedFilesInput, setAcceptedFilesInput] = useState(null)
    const [openFileBrowser, setOpenFileBrowser] = useState(0); // This state is for opening up the file browser whenever it is incremented by 1
    const { channelsBlockState, streamDeletedUsers, inputRef, myFriendsId } = useContext(CustomStreamContext)
    const [isDeletedUser1to1, setIsDeletedUser1to1] = useState(get1to1ChannelStatus(channel, streamDeletedUsers));
    const [showEmojiPicker, setShowEmojiPicker] = useState(false)
    const client = useClient()
    const { setModal } = useContext(ModalContext);
    const { user } = useContext(UserContext);
    const [optionsActive, setOptionsActive] = useState(false);

    const {
        text = '',
        setText,
        handleEmojiKeyDown,
        handleSubmit,
        uploadNewFiles,
        numberOfUploads,
        attachments,
        removeAttachments
    } = useMessageInputContext();

    useEffect(() => {
        if (acceptedFilesInput) {
            attachmentUploadInput?.current?.click()
        }
    }, [openFileBrowser])

    useEffect(() => {
        setIsDeletedUser1to1(get1to1ChannelStatus(channel, streamDeletedUsers))
    }, [streamDeletedUsers])

    //Commenting may be used later
    // useEffect(() => {
    //     // This is to focus on the text area by default when the component mounts
    //     document?.querySelectorAll('.rta__textarea')?.[0]?.focus()
    // }, [])

    const uploadFiles = async () => {
        const filesToUpload = [], failedFiles = [];
        for (let i = 0; i < attachmentUploadInput?.current?.files?.length; i++) {
            const currentFile = attachmentUploadInput?.current?.files[i];
            if (currentFile?.type?.includes('image/') && currentFile?.size > SIZE_IN_MB) {
                failedFiles.push(attachmentUploadInput?.current?.files[i])
            } else if (currentFile?.type?.includes('image/') && currentFile?.size <= SIZE_IN_MB) {
                const compressedImage = await compressImage({ file: attachmentUploadInput?.current?.files[i] })
                filesToUpload.push(compressedImage)
            } else {
                filesToUpload.push(attachmentUploadInput?.current?.files[i])
            }
        }

        if (failedFiles.length) {
            addNotification(ERROR(failedFiles?.length), 'error')
        }

        // Upload the files on stream
        uploadNewFiles(filesToUpload)
    }

    if (channelsBlockState[channel?.data?.id]?.blocked || isDeletedUser1to1 || channel?.data?.frozen) {
        return <DisabledMessageInput
            channelsBlockState={channelsBlockState[channel?.data?.id]}
            channel={channel}
            isDeletedUser={isDeletedUser1to1}
        />
    }

    const handleCreateOffer = async () => {
        try {
            await fetch(`../..${ENDPOINTS?.CAN_CREATE_OFFER}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_id: user.id
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (data.canCreate) {
                        setModal({
                            title: 'Create Offer',
                            img: {
                                src: '/svg/offer-outline.svg',
                                style: { height: 48, marginBottom: 10 },
                            },
                            width: 829,
                            type: 'group-offer',
                            groupName: getChannelName(channel, user, myFriendsId),
                            groupId: channel?.id,
                            source: "chat"
                        });
                    } else {
                        setModal({
                            title: 'Offer Limit',
                            message: data?.message,
                            type: 'warning',
                        })
                    }
                })
                .catch(err => console.log('Error'));
            await fetch(`../..${ENDPOINTS?.CAN_CREATE_OFFER}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    user_id: user.id
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (data.canCreate) {
                        setModal({
                            title: 'Create Offer',
                            img: {
                                src: '/svg/offer-outline.svg',
                                style: { height: 48, marginBottom: 10 },
                            },
                            width: 829,
                            type: 'group-offer',
                            groupName: getChannelName(channel, user, myFriendsId),
                            groupId: channel?.id,
                            source: "chat"
                        });
                    } else {
                        setModal({
                            title: 'Offer Limit',
                            message: data?.message,
                            type: 'warning',
                        })
                    }
                })
                .catch(err => console.log('Error'));
        } catch (error) {
            console.log("Can Create Offer-----", error);
        }
    }

    const sendAttachmentsIndividually = async () => {
      try {
        await forEach(attachments, async (attachment, index) => {

            let messageData = {
                text: index === 0 ? text : '',
                attachments: [],
            };
            messageData.attachments.push({
                type: 'image',
                image_url: attachment.image_url,
                thumb_url: attachment.image_url,
                originalImage: attachment.fallback,
            });
            const response = await channel.sendMessage(messageData);
            removeAttachments([attachment.localMetadata?.id])
        })
      } catch (error) {
        console.log("Error sending attachments:", error);
      }
    };

    return (
        <div ref={inputRef} className='flex-col flex-center'>
            {quotedMessage ?
                <div className="w-full mt-sm px-32" >
                    <QuotedMessagePreview quotedMessage={quotedMessage} channel={channel} />
                </div>
                : ''}
            <div className='flex w-[94%] px-sm md:px-[1%] justify-evenly my-sm items-center'>
                <div className='relative flex items-center'>
                    <div
                        className='min-w-[20px] mr-sm'
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                        onKeyDown={handleEmojiKeyDown}
                        role='button'
                        tabIndex={0}
                    >
                        <img src='/svg/MessageReaction.svg' width="20" />
                    </div>
                    {showEmojiPicker &&
                        <div
                            onMouseLeave={() => setShowEmojiPicker(false)}
                            className='absolute z-10 mr-sm' style={{ bottom: 50 }}>
                            <Picker data={data} onEmojiSelect={(e) => setText(text + e.native)} theme={'light'} />
                        </div>
                    }


                    <div className={`${(maxNumberOfFiles !== undefined && numberOfUploads >= maxNumberOfFiles) ? "cursor-not-allowed" : "cursor-pointer"}`}>
                        {
                            (maxNumberOfFiles !== undefined && numberOfUploads >= maxNumberOfFiles) ?
                                <div className="relative more-attachments-not-allowed">
                                    <AttachmentIcon />
                                    <div className="tooltip-tip rounded shadow w-48">Upto {maxNumberOfFiles} files are allowed to attach at once</div>
                                </div>
                                :
                                <OptionsButton
                                    optionsImage={[SYSTEM_THOUSAND_GREENS_PUBLIC, USER_CREATED_GROUP].includes(channel?.type)
                                        ? ['/svg/Chat/PhotosVideos.svg', '/svg/Chat/Document.svg', '/svg/Chat/Poll.svg']
                                        : ['/svg/Chat/PhotosVideos.svg', '/svg/Chat/Document.svg', '/svg/Chat/Event.svg', '/svg/Chat/Offer.svg', '/svg/Chat/Poll.svg']}
                                    options={{
                                        'Photos and Videos': () => {
                                            setOpenFileBrowser(prev => prev + 1);
                                            setAcceptedFilesInput(PHOTO_VIDEO?.join(", "));
                                        },
                                        'Documents': () => {
                                            setAcceptedFilesInput(DOCUMENTS?.join(", "));
                                            setOpenFileBrowser(prev => prev + 1);
                                        },
                                        ...(channel?.type === MY_TG_GROUP && {
                                            'Create Event': () => {
                                                setModal({
                                                    title: 'Create New Event',
                                                    img: {
                                                        src: '/svg/profile-play-active.svg',
                                                        style: { height: 36, marginBottom: 30 },
                                                    },
                                                    type: 'event',
                                                    width: "878px",
                                                    source: "chat",
                                                    channelName: getChannelName(channel, user, myFriendsId),
                                                    groupId: channel?.id
                                                });
                                            },
                                            'Create Offer': () => {
                                                handleCreateOffer()
                                            },
                                        }),
                                        ...(![ONE_TO_ONE, REQUEST_CHAT_GROUP].includes(channel?.type) && {
                                            'Create Poll': () => {
                                                setModal({
                                                    width: 542,
                                                    type: 'create-poll',
                                                    channel
                                                });
                                            }
                                        })

                                    }}
                                    labelContainerStyle='border-none'
                                    setOptionsActive={setOptionsActive}
                                    IconComponent={() => <AttachmentIcon isActive={optionsActive} />}
                                    top={[REQUEST_CHAT_GROUP]?.includes(channel?.type) ? -80 : [SYSTEM_THOUSAND_GREENS_PUBLIC, USER_CREATED_GROUP, REQUEST_CHAT_GROUP].includes(channel?.type) ? -100 : [ONE_TO_ONE].includes(channel?.type) ? -67 : -168}
                                    left={5}
                                    width={155}
                                />
                        }
                    </div>
                </div>

                <div className={`relative flex-1 mx-18px cus-input-container font-normal ${numberOfUploads ? "with-attachment" : ''} `}>
                    {
                        numberOfUploads ?
                            <AttachmentPreviewList />
                            : ''
                    }
                    <ChatAutoComplete onFocus={(e) => { e.preventDefault() }} placeholder='Type a message' />
                </div>

                <div className='cursor-pointer min-w-[20px] ml-sm'
                    onClick={(e) => {
                        if ((text?.length && text?.trim() !== "") || numberOfUploads) {
                            if (attachments?.length < 4) {
                                sendAttachmentsIndividually()
                            } else {
                                handleSubmit(e)
                            }
                        }
                    }}
                >
                    <img src='/svg/SendMessage.svg' />
                </div>

                {/* The below input field is for opening the Media Files Browser in the user's system */}
                <input
                    id='attachment'
                    name='attachment'
                    ref={attachmentUploadInput}
                    disabled={(maxNumberOfFiles !== undefined && numberOfUploads >= maxNumberOfFiles)}
                    type="file"
                    multiple={multipleUploads}
                    onInput={() => uploadFiles()}
                    onClick={e => {
                        e.target.value = null
                    }} // We are setting this to null because we want to be able to select the same file simultaneously
                    style={{ visibility: 'hidden', width: 0, height: 0 }}
                    accept={acceptedFilesInput}
                />
            </div>
        </div>
    )
}

export default CustomInput;
