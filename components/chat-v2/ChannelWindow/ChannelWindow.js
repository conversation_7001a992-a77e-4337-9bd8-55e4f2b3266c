import React, { useContext, useEffect, useState } from 'react'
import { Window, MessageInput, useChannelStateContext, useChatContext, VirtualizedMessageList } from 'stream-chat-react'
import ChannelHeader from './ChannelHeader'
import CustomMessage from './CustomMessage'
import CustomInput from './CustomInput'
import { ModalContext } from '../../../context/ModalContext'
import { UserContext } from '../../../pages/_app'
import constantOptions from '../../../constants/constantOptions'
import { CustomStreamContext } from '../../../context/CustomStreamContext'
import streamOptions from '../../../constants/streamOptions'
import { DrawerContext } from '../../../context/DrawerContext'
import useCheckDeviceScreen from '../../../hooks/useCheckDeviceScreen'
import CustomMobileInput from './CustomMobileInput'

const { ONE_TO_ONE, SYSTEM_THOUSAND_GREENS_PUBLIC, REQUEST_CHAT_GROUP } = streamOptions?.CHANNEL?.TYPES

const { NOTIFICATION } = constantOptions?.SOURCES
const { POLL_DETAILS } = constantOptions?.DRAWER_TYPE


const actions = ['quote'];

const ChannelWindow = ({ setReRenderForcefully, request }) => {
  const { setModal } = useContext(ModalContext)
  const { user, streamChatClient } = useContext(UserContext)
  const { updateMembersOfChannel, updateBlockStateOfChannel, addMutedChannel, setOtherUserId } = useContext(CustomStreamContext)
  const { channel } = useChannelStateContext()
  const { setActiveChannel } = useChatContext()
  const { drawer, setDrawer, renderChatDrawer } = useContext(DrawerContext)
  const [referredId, setReferredId] = useState('')
  const { isMobile, isTablet } = useCheckDeviceScreen()


  useEffect(() => {
    const referredId = window.sessionStorage.getItem("REFERAL_NOTIFICATION_DATA")
    if (referredId) {
      setReferredId(referredId)
      setOtherUserId(referredId)
    }
  }, [])

  useEffect(() => {
    if (referredId) {
      setDrawer({
        type: "profile",
      })
      sessionStorage?.removeItem("REFERAL_NOTIFICATION_DATA")
    }
  }, [referredId])

  useEffect(() => {
    if (channel?.type !== REQUEST_CHAT_GROUP && drawer?.source !== NOTIFICATION) {
      setDrawer(true)
    }

  }, [])

  useEffect(() => {
    if (channel?.type !== REQUEST_CHAT_GROUP) {
      updateMutedChannels()

      /**
       * The below if the code for listening to the channel update event
       * We are doing this because channel context does not update immediately when the group detail is changed
      */
      const channelUpdatedEventListener = async (event) => {

        if (channel?.data?.type === ONE_TO_ONE) {
          await channel.watch();
        }

        if (channel?.data?.name !== event?.channel?.name || channel?.data?.image !== event?.channel?.image) {
          await channel.watch();
        }

        //After any update in channel, calling this function to update block related state in the context 
        updateBlockStateOfChannel(event?.channel)
      }
      const channelUpdatedListener = channel?.on('channel.updated', channelUpdatedEventListener);

      /**
       * The below event listener is for listening to the changes when members are updated in a channel
       * We are using this for updating the members list in the context
       */
      const memberUpdatedListener = channel?.on('member.updated', async (event) => {
        await updateMembersOfChannel(event?.channel_id)
      })
      const memberAddedListener = channel?.on('member.added', async (event) => {
        await updateMembersOfChannel(event?.channel_id, event?.member, 'add')
      })
      const memberRemovedListener = channel?.on('member.removed', async (event) => {
        if (event?.user?.id !== user?.id) {
          await updateMembersOfChannel(event?.channel_id, event?.member, 'remove')
        } else {
          setReRenderForcefully(prev => prev + 1)
          setActiveChannel(null)
        }
      })

      //Update the channel context after page refresh
      updateBlockStateOfChannel(channel?.data)
      if (drawer?.source !== NOTIFICATION) {
        setDrawer()
      }

      return () => {
        channelUpdatedListener?.unsubscribe();
        memberUpdatedListener?.unsubscribe()
        memberAddedListener?.unsubscribe()
        memberRemovedListener?.unsubscribe()
      }

    }
  }, [channel])

  const deleteMessageHandler = async (messageId) => {
    if (streamChatClient) {
      await streamChatClient?.deleteMessage(messageId)
    }
  }

  const customActions = {
    'Copy': (message) => {
      navigator.clipboard.writeText(message?.text || '');
    },
    'Delete': (message) => {
      setModal({
        type: "delete-chat",
        width: 430,
        onClickHandler: () => deleteMessageHandler(message?.id),
        text: 'Are you sure you want to delete this message?'
      })
    },
    ...(channel?.type !== REQUEST_CHAT_GROUP && {
      'Forward': (message) => {
        setModal({
          type: 'forward-message',
          width: 500,
          messageId: message?.id
        });
      }
    })
  }

  /**
   * 1. When we select a channel we will call setActiveChannel of stream to update the context
   * 2. If the selected channel is muted then we add update our own context
   */
  const updateMutedChannels = async () => {
    const { muted } = channel?.muteStatus();
    if (muted) {
      addMutedChannel(channel?.id, 0)
      await channel.markRead();
    }
  }

  return (
    <>
      <Window>
        <ChannelHeader request={request} />
        <VirtualizedMessageList
          Message={CustomMessage}
          customMessageActions={customActions}
          messageActions={actions}
          messageLimit={constantOptions?.STREAM_MESSAGE_LIMIT}
          internalInfiniteScrollProps={{
            threshold: 750
          }}
        >
        </VirtualizedMessageList>
        <MessageInput
          grow={true}
          Input={() => (isMobile || isTablet) ? <CustomMobileInput /> : <CustomInput />}
          focus={true}
          maxRows={5}
          additionalTextareaProps={{
            maxLength: 5000
          }}
          disableMentions={[SYSTEM_THOUSAND_GREENS_PUBLIC, ONE_TO_ONE].includes(channel?.data?.type) ? true : false}
        />
      </Window>

      {/* GLOBAL FUNCTION FOR RENDERING CHAT DRAWERS */}
      {(channel?.type !== REQUEST_CHAT_GROUP || drawer?.type === POLL_DETAILS) ? (
        renderChatDrawer()
      ) : null}
    </>
  )
}

export default ChannelWindow