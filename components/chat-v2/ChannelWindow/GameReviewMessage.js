import { useContext } from 'react';
import useThumbnail from '../../../hooks/useThumbnail';
import { ChatGalleryViewModalContext } from "../../modals/ChatGalleryViewModal";
import { UserContext } from '../../../pages/_app';

const GameReviewMessage = ({ message }) => {

    const { thumbnailUrl } = useThumbnail(message?.gameReview?.photo, 200)
    const { setChatImageModal } = useContext(ChatGalleryViewModalContext);
    const { user } = useContext(UserContext)

    return (
        <div className='px-md py-sm w-[343px]'>
            <div className='flex flex-col'>
                <div className='text-16 font-medium pr-xs'>{message?.gameReview?.clubName}</div>
                <div className={`${user?.id === message?.user?.id ? ' text-white' : 'text-gray'} `}>Game Date: {message?.gameReview?.gameDate}</div>
            </div>
            {message?.gameReview?.photo &&
                <div className='rounded-lg w-full my-sm cursor-pointer'
                    onClick={() => {
                        setChatImageModal({
                            images: [{ image_url: thumbnailUrl, fallback: thumbnailUrl, type: "image" }],
                            fallback: thumbnailUrl,
                            imageIndex: 0,
                        });
                    }}
                    style={{
                        height: 300,
                        backgroundImage: `url("${thumbnailUrl}")`,
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                    }}>
                </div>
            }
            <div>
                <span className=''>{message?.gameReview?.review}</span>
            </div>
        </div>
    )
}

export default GameReviewMessage;