import React, { useState, useRef, useEffect, useCallback } from 'react'
import { TextInput, ToolTip } from '../common'
import { ThreeDots } from 'react-loader-spinner'
import ENDPOINTS from "../../constants/endpoints.json"

function debounce(func, wait, immediate) {
    var timeout;

    return (...args) => {
        var context = this;

        var later = () => {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };

        var callNow = immediate && !timeout;

        clearTimeout(timeout);

        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
    };
}

export default function Select({
    placeholder,
    title,
    error,
    type,
    club,
    value,
    options: initialOptions,
    update,
    updateSearch,
    defaultInitial,
    className,
    disableError,
    tooltip,
    globalDropdownState,
    user,
    token,
    extraClasses = "",
    extraStyles = {},
    removeSearchError = () => { },
    addClubClasses,
    readOnly = false,
    editClubClasses,
    tooltipStyles = {},
    customAddressClasses,
    groupId,
    groupParticipants,
    hostsSent,
    requestId
}) {

    const [options, setOptions] = useState(initialOptions ? initialOptions : [])
    const [dropdownVisible, setDropdownVisible] = useState(false)
    const [loading, setLoading] = useState(false)
    const [userData, setUserData] = useState([])
    const [hostsData, setHostsData] = useState()
    function useOutsideAlerter(ref) {
        useEffect(() => {
            /**
             * Alert if clicked on outside of element
             */
            function handleClickOutside(event) {
                if (ref.current && !ref.current.contains(event.target)) {
                    setDropdownVisible(false);
                }
            }

            // Bind the event listener
            document.addEventListener("mousedown", handleClickOutside);
            return () => {
                // Unbind the event listener on clean up
                document.removeEventListener("mousedown", handleClickOutside);
            };
        }, [ref]);
    }
    const wrapperRef = useRef(null);
    useOutsideAlerter(wrapperRef);

    function toggleDropdown() {
        if (!value && dropdownVisible && defaultInitial) {
            update(options[0])
        }
        setDropdownVisible(false)
    }

    //We are passing types from the parent component, and accordingly API is triggered and resulting data is handled
    const searchType = ['admin-search-participants', 'admin-search-hosts']

    useEffect(() => {
        if (searchType.includes(type)) {
            if (value && value.length > 0) {
                setLoading(true)
                fetchSearchResults(value);
                setDropdownVisible(true);
            } else {
                setOptions([]);
                setDropdownVisible(false);
            }
        } else {
            window.addEventListener('click', toggleDropdown)
            return () => {
                window.removeEventListener('click', toggleDropdown)
            }
        }
    }, [value])

    const fetchSearchResults = useCallback(
        debounce((value) => {
            let url
            switch (type) {
                case 'admin-search-participants':
                    url = ENDPOINTS.ADMIN_SEARCH_PARTICIPANTS
                    break;
                case 'admin-search-hosts':
                    url = ENDPOINTS?.ADMIN_SEARCH_HOSTS
            }
            fetch(url,
                {
                    method: 'POST',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': "application/json",
                        ['Authorization']: `Bearer ${token}`
                    },
                    body: JSON.stringify(
                        (type === 'admin-search-participants')
                            ? {
                                groupId: groupId,
                                search: value || '',
                                limit: 100,
                                page: 0
                            }
                            : (type === 'admin-search-hosts') ? {
                                search: `%${value}%` || '',
                                requestId,
                                userId: user?.id
                            } :
                                {
                                    search: value || '',
                                    userId: user?.id
                                }
                    ),
                }
            )
                .then((data) => data.json())
                .then((users) => {
                    setLoading(false)
                    if (type === 'admin-search-participants') {
                        setUserData(users?.userData)
                    }

                    if (type === 'admin-search-hosts') {
                        setHostsData(users?.hostData)
                    }
                })
        }, 250),
        []
    );

    useEffect(() => {
        setGlobalDropdownVisible(false); // adding to close open dropdowns
        if (dropdownVisible) {
            setGlobalDropdownVisible(true)
            window.addEventListener('keydown', onKeyDown)
            return () => {
                window.removeEventListener('keydown', onKeyDown)
            }
        }
    }, [dropdownVisible, value])

    useEffect(() => {
        if (!globalDropdownVisible) {
            setDropdownVisible(false)
        }
    }, [globalDropdownVisible])


    useEffect(() => {
        if (groupParticipants?.length) {
            //Filter out already selected users
            let filteredData = []

            filteredData = userData.filter(user => {
                return groupParticipants.every(member => {
                    return member?.id != user?.id
                })
            })

            setOptions(filteredData)
        } else {
            setOptions(userData)
        }
    }, [userData])

    useEffect(() => {
        if (hostsData?.length) {
            setOptions(hostsData)
        }
    }, [hostsData])

    useEffect(() => {
        setOptions([])

    }, [value])

    const {
        globalDropdownVisible,
        setGlobalDropdownVisible,
    } = globalDropdownState
            ? globalDropdownState
            : { globalDropdownVisible: true, setGlobalDropdownVisible: () => { } }

    function onKeyDown(e) {
        if (e.keyCode === 13) {
            setDropdownVisible(false)
        }
        if (e.keyCode === 40 || e.keyCode === 38) {
            const keyDirection = e.keyCode === 38 ? 'up' : 'down'
            const currentValueIndex = options.indexOf(value)

            if (
                (currentValueIndex === 0 && keyDirection === 'up') ||
                (currentValueIndex === options.length - 1 &&
                    keyDirection === 'down')
            ) {
                return
            }
            const nextIndex =
                currentValueIndex === -1
                    ? 0
                    : currentValueIndex + (keyDirection === 'up' ? -1 : 1)

            update(options[nextIndex])
        }
    }

    return (
        <div
            className={`${extraClasses} flex flex-col ${disableError || error ? 'relative' : `${customAddressClasses ? '' : 'lg:mb-xl'}`
                } relative w-full`}
            style={{
                minHeight: 38,
                zIndex: dropdownVisible ? 60 : 50,
                ...extraStyles
            }}
            ref={wrapperRef}
        >
            {title && (
                <div className="flex items-center">
                    <div
                        className={` ${addClubClasses ? 'text-14 font-medium' : 'text-sm'} ${error ? 'text-red' : 'text-gray'
                            }  font-thin`}>
                        {title}
                    </div>
                    {tooltip && (
                        <div className={`flex-1 pl-1 relative`}>
                            <ToolTip tip={tooltip} style={tooltipStyles} />
                        </div>
                    )}
                </div>
            )}
            {searchType.includes(type) && club && (
                <div
                    className="absolute bg-darkteal flex-center text-white px-sm z-50 text-sm rounded"
                    style={{
                        top: 0,
                        paddingTop: 5,
                        paddingBottom: 5,
                    }}>
                    {club.name}
                    <img
                        onClick={() => {
                            updateSearch('')
                            update()
                        }}
                        className="relative cursor-pointer"
                        src="/svg/circle-close.svg"
                        style={{
                            height: 12,
                            width: 12,
                            marginLeft: 10,
                            bottom: 1,
                        }}
                    />
                </div>
            )}
            {searchType.includes(type) ? (
                <TextInput
                    disableError={true}
                    error={error}
                    onBlur={() => {
                        setDropdownVisible(false)
                        removeSearchError()
                    }
                    }
                    onFocus={() => setDropdownVisible(true)}
                    value={value}
                    update={updateSearch}
                    placeholder={placeholder}
                    addClubClasses={addClubClasses}
                    className={className}
                />
            ) : (
                <button
                    onClick={(e) => {
                        if (!readOnly) {
                            e.stopPropagation()
                            setGlobalDropdownVisible(false);
                            setDropdownVisible(true)
                        }
                    }}
                    role="button"
                    type="button"
                    style={{ outline: 0 }}
                    className={`${className} ${value ? 'text-black' : 'text-placeholder font-thin'
                        } cursor-pointer overflow-hidden capitalize py-sm text-left ${editClubClasses || addClubClasses ? "text-16 " : customAddressClasses ? "text-14" : "text-sm "} border-b ${error ? 'border-red' : `${customAddressClasses ? '' : 'border-lightgray'}`
                        }`}>
                    {value
                        ? typeof value === 'string' || typeof value === 'number'
                            ? value
                            : value.name
                        : placeholder}
                </button>
            )}
            {((dropdownVisible &&
                value &&
                value?.length > 0 &&
                (options?.length !== 0 || loading)) ||
                (dropdownVisible && type !== 'clubs' && type !== 'register-clubs')) && (
                    <div
                        className={`absolute flex flex-col text-12 overflow-scroll bg-white rounded-lg shadow-lg`}
                        style={{
                            top: 50,
                            minHeight: 40,
                            maxHeight: 200,
                            width: '100%',
                            zIndex: 100000000
                        }}>
                        {loading ? (
                            <div className="absolute inset-0 flex-center">
                                <ThreeDots
                                    visible={true}
                                    height="15"
                                    width="15"
                                    color={"#098089"}
                                    radius="9"
                                    ariaLabel="three-dots-loading"
                                    wrapperStyle={{}}
                                    wrapperClass=""
                                />                            </div>
                        ) : ((searchType.includes(type)) && !(value)) ? null :
                            options && options.length ?
                                (
                                    options.map((option, i) => {
                                        return (
                                            <div
                                                key={i}
                                                onMouseDown={() => {
                                                    if (
                                                        type === 'admin-search-participants' || type === 'admin-search-hosts'
                                                    ) {
                                                        update(option)
                                                        setDropdownVisible(false)
                                                    }
                                                }}
                                                onClick={(e) => {
                                                    if (
                                                        typeof option !== 'object' ||
                                                        !option.disabled
                                                    ) {
                                                        e.stopPropagation()
                                                        update(option)
                                                        setDropdownVisible(false)
                                                    }
                                                }}
                                                className={`p-sm hover:bg-lightestgray ${typeof option === 'object' &&
                                                    option.disabled
                                                    ? 'text-gray'
                                                    : 'text-black'
                                                    } cursor-pointer ${value
                                                        ? value === option
                                                            ? 'font-bold'
                                                            : ''
                                                        : i === 0 && defaultInitial
                                                            ? 'font-bold'
                                                            : ''
                                                    }`}>
                                                {typeof option !== 'object'
                                                    ? option : (type === "admin-search-hosts") ? option?.username :
                                                        option?.first_name + " " + option?.last_name + " - " + option?.email}
                                            </div>
                                        )
                                    })
                                )
                                :
                                <div
                                    className="p-sm hover:bg-lightestgray capitalize">
                                    No users matching the entered text
                                </div>
                        }
                    </div>
                )
            }
            {
                !disableError && error && (
                    <div
                        className="text-xs font-thin flex-center text-center"
                        style={{ minHeight: 40, color: 'red' }}>
                        {error}
                    </div>
                )
            }
        </div >
    )
}
