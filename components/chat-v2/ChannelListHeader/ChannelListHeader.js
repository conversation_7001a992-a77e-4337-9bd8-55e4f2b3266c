import React, { useContext, useEffect, useState } from 'react'
import { CustomStreamContext } from '../../../context/CustomStreamContext'
import OptionsButton from '../common/OptionsButton'
import SearchInput from '../../common/SearchInput'
import streamOptions from '../../../constants/streamOptions'
import ChatHeader from '../common/ChatHeader'
import useCheckDeviceScreen from '../../../hooks/useCheckDeviceScreen'
import ThreeDotsIcon from '../../icons/ThreeDotsIcon'

const COMPONENT = streamOptions?.CHAT_LIST_CONSTANTS

/**
 * This Component is for showing the channel header that will be placed above the channel list
 * @param {*} param
 * @returns
 */
const ChannelListHeader = (props) => {
    const { onSearch, query, disabled, clearState = () => { }, setQuery, selectedChatType, setSelectedChatType } = props
    const {
        updateNewChatComponent,
        showArchiveList,
        setshowArchiveList,
    } = useContext(CustomStreamContext)
    const inputRef = React.createRef()
    const { isMobile, isTablet } = useCheckDeviceScreen()

    useEffect(() => {
        setQuery(query)
    }, [query])

    const showNewChatHandler = () => {
        // We have to dismiss the chat before showing the new chat list and for this purpose
        // we have created a ref inside the search input bar
        // because there was no direct way to cancel/exit the search
        inputRef?.current?.click()
        updateNewChatComponent(COMPONENT?.NEW_1_TO_1_CHAT)
    }

    const showActionMenu = (e) => {
        let options = {
            ['Blocked Users']: () => {
                updateNewChatComponent(COMPONENT?.BLOCKED)
            },
        }

        if (isMobile) {
            options = {
                ...options,
                Archived: () => {
                    setshowArchiveList((prev) => !prev)
                    clearState()
                    // onSearch(e)
                }
            }
        }

        return (
            <OptionsButton
                IconComponent={(props) => <ThreeDotsIcon {...props} width={29.5} height={29.5} />}
                top={'30px'}
                right={'20px'}
                width={120}
                options={options}
                iconStyle='rounded-full'
            />
        )
    }

    if (showArchiveList) {
        return <ChatHeader />
    }

    return (
        <div className="flex flex-col w-full">
            <div className={`sticky-header flex px-md lg:gap-2 md:gap-1 flex-no-wrap h-channel-list-header w-full ${!isMobile ? 'justify-between' : ''} items-center bg-white`}>
                <div className={`lg:flex-grow-0.7 ${isMobile && 'w-full'}`}>
                    <SearchInput
                        placeholder="Search"
                        onChange={onSearch}
                        value={query}
                        disabled={disabled}
                        onClearInput={(e) => {
                            clearState()
                            onSearch(e)
                        }}
                        ref={inputRef}
                        maxWidth={500}
                    />
                </div>
                <div className={`self-center cursor-pointer ${(!isMobile && !isTablet) ? 'ml-28px' : 'ml-md'}`}>
                    <img
                        src="/svg/create-new-channel.svg"
                        // className="lg:w-30px md:w-25px"
                        style={{ minWidth: 28 }}
                        onClick={() => showNewChatHandler()}
                    />
                </div>
                <div className={`${!isMobile ? 'self-center ml-sm' : 'ml-md'} cursor-pointer`}>
                    {showActionMenu()}
                </div>
            </div>
            {!isMobile &&
                <div
                    className="after-elm pb-md pt-sm cursor-pointer pl-6 items-center flex"
                    onClick={(e) => {
                        setshowArchiveList((prev) => !prev)
                        clearState()
                        onSearch(e)
                    }}>
                    <div>
                        <img src="/svg/archiveTeal.svg" className="" />
                    </div>
                    <div className="text-14 font-medium text-black ml-20px ">
                        Archived
                    </div>
                </div>}
            <div className="flex flex-row gap-2 px-md py-sm">
                {streamOptions?.CHAT_TYPES?.map((type) => (
                    <div key={type.value} className={`flex flex-row gap-2 cursor-pointer text-12 font-medium px-md py-sm rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 ${selectedChatType === type.value ? 'bg-darkteal text-white' : 'bg-none text-black border border-black'}`}
                        onClick={() => setSelectedChatType(type.value)}>
                        {type.label}
                    </div>
                ))}
            </div>
        </div>
    )
}

export default ChannelListHeader
