import React, { useEffect, useState, useContext } from 'react'
import { MapAllMyFriendsListItem } from '../clubs/components/MapAllMyFriendsListItem'
import CustomButton from '../buttons/CustomButton'
import { ThreeDots } from 'react-loader-spinner'
import PulseSkeleton from '../common/PulseSkeleton'
import ENDPOINTS from "../../constants/endpoints.json"
import InfiniteScroll from 'react-infinite-scroll-component'
import { UserContext } from '../../pages/_app'
import { ModalContext } from '../../context/ModalContext'
import useClient from '../../graphql/useClient'
import { UPDATE_ADDITIONAL_SETTINGS } from '../../graphql/mutations/user'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'
import { DrawerContext } from '../../context/DrawerContext'
import useDrawerHeightCalculator from '../../hooks/useDrawerHeightCalculator'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'


export const MapAllMyFriendsDrawer = ({ }) => {

    const [friends, setFriends] = useState([])
    const [page, setPage] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    const [loading, setLoading] = useState(false)
    const { token, user, fetchUser } = useContext(UserContext)
    const { modal, setModal } = useContext(ModalContext)
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
    const client = useClient()
    const [friendsId, setFriendsId] = useState([])
    const [mutedFriendsCount, setMutedFriendsCount] = useState(0)
    const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
    const { club, clubColor } = drawer
    const disableRequestToAll = mutedFriendsCount === friends?.length
    const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
    const isSelfclub = user?.clubs?.filter((c) => c?.id === club?.id)
    const MAP_CAN_CREATE_REQUEST = window?.sessionStorage?.getItem("MAP_CAN_CREATE_REQUEST")
    const { maintenanceStatus } = useMaintenanceStatus()
    const { isDesktop, isWideScreen, isTablet, isMobile } = useCheckDeviceScreen()

    useEffect(() => {
        fetchFriends()
        checkToken()
    }, [])

    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
        }
    }, [user])

    useEffect(() => {
        if (friends?.length) {
            const count = friends.filter((friend) => friend?.is_muted)
            setMutedFriendsCount(count?.length);
        }
    }, [friends])

    useEffect(() => {
        const fetchAllFriendsId = async () => {
            try {
                await fetch(ENDPOINTS?.MAP_GET_FRIENDS_ID, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        search: club?.name
                    }),
                })
                    .then((data) => data.json())
                    .then((data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            setFriendsId(data?.friends);
                        }
                    })
                    .catch((error) => {
                        console.log(error)
                    })
                setModal()
            } catch (error) {
                console.log("Map All My Friends List-------->", error);
            }
        }

        fetchAllFriendsId()
    }, [club?.name])

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    const fetchFriends = async (pageToLoad = page) => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS.MAP_MY_FRIENDS, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    page: pageToLoad,
                    limit: pageSize,
                    search: club?.name
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    if (pageToLoad !== 1) {
                        setFriends((prev) => [...prev, ...data?.friends])
                        setTotalCount(data?.totalCount)
                    } else {
                        setFriends(data?.friends)
                        setTotalCount(data?.totalCount)
                    }
                })
            setPage(pageToLoad)
            setLoading(false)
        } catch (error) {
            console.log("Map All My friends list----", error);
        }
    }

    async function checkToken() {
        fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: user.id,
                isRequestToAll: false
            }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null);
                } else {
                    setRequestRestricted(data.message);
                }
            })
            .catch(err => console.log('Error'));
    }

    const handleCreateRequest = (requestType, friendId) => {
        if (requestRestricted) {
            setModal({
                title: 'Request Limit',
                message: requestRestricted,
                type: 'warning',
            })
        } else {
            if (showCreateRequestPopup) {
                setModal({
                    club,
                    friendId,
                    requestType: requestType,
                    type: 'request-confirmation',
                    width: 500,
                    setShowCreateRequestPopup: (val) => {
                        setShowCreateRequestPopup(val)
                        setModal({
                            club,
                            type: 'request-against-offer',
                            title: "Create Request",
                            img: {
                                src: '/svg/request-offer.svg',
                                style: {
                                    height: 80,
                                    marginBottom: 10,
                                },
                            },
                            friendId,
                            requestType: requestType,
                            width: 530
                        })
                    },
                })
            } else {
                setModal({
                    club,
                    type: 'request-against-offer',
                    title: "Create Request",
                    img: {
                        src: '/svg/request-offer.svg',
                        style: {
                            height: 80,
                            marginBottom: 10,
                        },
                    },
                    friendId,
                    requestType: requestType,
                    width: 530
                })
            }
        }
    }

    return (
        <div
            id="map-drawer-clubs"
            className='bottom-0 right-0 h-full'
            style={{
                width: (isDesktop || isWideScreen || isTablet) ? '500px' : '100%',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.14)',
                zIndex: 2
            }}>
            <div className="relative bg-white w-full h-full flex flex-col items-center">
                {loading && (
                    <div className='absolute w-full flex-center h-full ' style={{ zIndex: 99999 }}>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                )}
                <div
                    className="pl-[2px] absolute w-full"
                    style={{
                        bottom: 45, // Add space for the button at bottom
                        top: 0,
                        overflowX: 'hidden'
                    }}>
                    <div className={`flex justify-between flex-center relative bg-lightestgray min-h-[58px]`} >
                        {drawer?.customBackClickHandler &&
                            <div className='flex text-16 font-medium absolute cursor-pointer text-gray'
                                style={{ left: 10 }}
                                onClick={() => {
                                    drawer?.returnToMapInfo()
                                }}
                            >
                                <img width={15}
                                    style={{ transform: 'rotate(90deg)', marginRight: 10 }}
                                    src='/svg/arrow-down.svg' />Back
                            </div>
                        }
                        <div className='text-18 md:text-20 font-medium h-[58px] flex-center bg-lightestgray relative'>My Friends in this Club</div>
                        <div
                            onClick={() => {
                                setDrawer()
                            }}
                            className="cursor-pointer absolute" style={{ right: 20, top: 20 }}>
                            <img src='/svg/CrossIconBlack.svg' />
                        </div>
                    </div>
                    <div className='w-full flex-1'>
                        <InfiniteScroll
                            height={(window.innerHeight - 220)}
                            dataLength={friends?.length}
                            next={() => fetchFriends((page + 1))}
                            hasMore={friends?.length === pageSize * page}
                            loader={<CustomLoader />}
                            scrollThreshold={0.8}>
                            {friends?.map((friend) => (
                                <MapAllMyFriendsListItem
                                    key={friend?.friend_id}
                                    friend={friend}
                                    club={club}
                                    requestType={"FRIEND"}
                                    handleCreateRequest={handleCreateRequest}
                                    isSelfclub={isSelfclub?.length}
                                    clubColor={clubColor}
                                    canCreateRequest={drawer?.canCreateRequest || MAP_CAN_CREATE_REQUEST}
                                    customBackClickHandler={drawer?.customBackClickHandler}
                                />
                            )
                            )
                            }
                        </InfiniteScroll>
                    </div>
                </div>
                <div className={`absolute w-full flex bottom-0 left-0 py-sm shadow-lg bg-white py-sm`}>
                    <CustomButton
                        height={45}
                        width={"100%"}
                        text='Send Message to all'
                        buttonImage={isMobile ? '' : '/svg/SendMessageTiltedWhite.svg'}
                        onClick={() => {
                            setModal({
                                type: "broadcast-message",
                                clubFriends: friendsId
                            })
                        }} />
                    {(!disableRequestToAll && !isSelfclub.length && (drawer?.canCreateRequest || MAP_CAN_CREATE_REQUEST)) &&
                        <CustomButton
                            height={45}
                            width={"100%"}
                            text='Create Request to all'
                            buttonImage={isMobile ? '' : '/svg/CreateRequestWhite.svg'}
                            onClick={() => {
                                if (maintenanceStatus?.request) {
                                    toastNotification({
                                        type: constantOptions.TOAST_TYPE.ERROR,
                                        message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                    })
                                } else {
                                    handleCreateRequest("ALL_MY_FRIENDS")
                                }
                            }}
                        />}
                </div>
            </div>
        </div>
    )
}

const CustomLoader = () => <div className="ml-m-7px"><PulseSkeleton times={3} /></div>

