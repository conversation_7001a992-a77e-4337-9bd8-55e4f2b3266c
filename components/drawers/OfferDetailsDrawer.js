import React, { useContext, useState, useEffect } from 'react'
import CustomButton from '../buttons/CustomButton'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { DrawerContext } from '../../context/DrawerContext'
import { UserContext } from '../../pages/_app'
import { ThreeDots } from 'react-loader-spinner'
import useClient from '../../graphql/useClient'
import { ModalContext } from '../../context/ModalContext'
import { UPDATE_ADDITIONAL_SETTINGS } from '../../graphql/mutations/user'
import ENDPOINTS from '../../constants/endpoints.json'
import { useRouter } from 'next/router'
import moment from 'moment'
import constantOptions from '../../constants/constantOptions'
import NameInitials from '../common/NameInitials'
import dateFormatter from '../../utils/helper/dateFormatter'
import SuperHostTag from '../common/SuperHostTag'
import FcmTag from '../common/FcmTag'
import AmbassadorTag from '../common/AmbassadorTag'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import { getTier } from '../../utils/tiers'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'
import toastNotification from '../../utils/notifications/toastNotification'

const { PROFILE, GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE

const OfferDetailsDrawer = () => {
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const { setOtherUserId } = useContext(CustomStreamContext)
    const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
    const { offer, refresh = () => { } } = drawer
    const { user, token, fetchUser } = useContext(UserContext)
    const [offerDetails, setOfferDetails] = useState({})
    const isSelfRequest = offer?.user_id === user?.id || offerDetails?.user_id === user?.id
    const isSefClub = user?.clubs?.map((club) => club?.id).includes(drawer?.clubId)
    const [isShowLoading, setIsShowLoading] = useState(false)
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState(true);
    const client = useClient()
    const { setModal } = useContext(ModalContext)
    const router = useRouter()
    const [refreshOfferDetails, setRefreshOfferDetails] = useState(0)
    const { maintenanceStatus } = useMaintenanceStatus()

    useEffect(() => {
        if (offer?.offer_id || drawer?.offerId) {
            fetchOfferDetails()
        }
    }, [offer?.offer_id, refreshOfferDetails, drawer?.offerId])

    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
        }
        checkToken()
    }, [user])

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
            checkToken()
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    const fetchOfferDetails = () => {
        try {
            setIsShowLoading(true)
            fetch(`${ENDPOINTS.GET_OFFER_DETAILS}`, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                }, body: JSON.stringify({
                    userId: user?.id,
                    offerId: offer?.offer_id || drawer?.offerId
                }),
            }).then((res) => res.json())
                .then((data) => {
                    if (data?.status) {
                        setOfferDetails(data?.data)
                    } else {
                        setModal()
                        setDrawer()
                        router.push({
                            pathname: '/dashboard/errors/404'
                        })
                    }
                    setIsShowLoading(false)
                })
        } catch (error) {
            console.log("------->", error);
        }
    }

    async function checkToken() {
        let body = {
            user_id: user.id,
        }
        if (offerDetails?.creatorIsFriend) {
            body = { ...body, isRequestToAll: false }
        }
        fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null);
                } else {
                    setRequestRestricted(data.message);
                }
            })
            .catch(err => console.log('Error'));
    }

    const handleRequest = () => {

        if (requestRestricted) {
            setModal({
                title: 'Request Limit',
                message: requestRestricted,
                type: 'warning',
            })
        } else {
            if (showCreateRequestPopup) {
                setModal({
                    title: 'Request against Offer',
                    width: 475,
                    type: 'request-confirmation-against-offer',
                    offer: offerDetails,
                    refresh: () => setRefreshOfferDetails(prev => prev + 1),
                    setShowCreateRequestPopup: (val) => setShowCreateRequestPopup(val)
                })
            } else {
                setModal({
                    title: 'Request against Offer',
                    img: {
                        src: '/svg/request-offer.svg',
                        style: {
                            height: 80,
                            marginBottom: 10,
                        },
                    },
                    width: 475,
                    refresh: () => setRefreshOfferDetails(prev => prev + 1),
                    type: 'request-against-offer',
                    offer: offerDetails,
                    creatorIsFriend: offerDetails?.creatorIsFriend,
                    requestType: 'OFFER',
                    textSize: '24px',
                    additionalStyles: {
                        fontWeight: 500
                    }
                })
            }
        }
    }

    return (
        <div className='fade-in bg-white shadow-lg z-50 relative' style={{ width: isMobile ? "100%" : "500px" }}>
            <div className='bg-lightestgray flex justify-between py-lm flex-center relative'>
                {drawer?.customBackClickHandler &&
                    <div className='flex text-16 font-medium absolute cursor-pointer text-gray'
                        style={{ left: 10 }}
                        onClick={() => {
                            drawer?.customBackClickHandler()
                        }}
                    >
                        <img width={15}
                            style={{ transform: 'rotate(90deg)', marginRight: 10 }}
                            src='/svg/arrow-down.svg' />Back
                    </div>
                }
                <div className='text-21 font-medium'>Offer Details</div>
                <div
                    onClick={() => {
                        setDrawer()
                        if (router?.query?.clubId) {
                            router.push({
                                pathname: '/dashboard/offers',
                            })
                        }
                    }}
                    className="cursor-pointer absolute right-[20px] top-[28px]">
                    <img src='/svg/CrossIconBlack.svg' />
                </div>
            </div>

            {
                isShowLoading ? (
                    <div className='flex-center h-[calc(100vh-58px)]'>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                ) : <div className='overflow-y-scroll'>

                    <div className='flex flex-col pt-md px-md'>
                        {offerDetails?.creator_id === user?.id &&
                            <div className='flex justify-start pb-md'>
                                <div className='text-10 font-medium bg-lightestgray rounded-lg px-md py-sm'>
                                    Your Own Offer
                                </div>
                            </div>
                        }
                        <div className='flex'>
                            <div className='h-[40px] w-[40px] flex-center rounded-full bg-tealTierBg'>
                                <img width={20} src="/svg/OfferNew.svg" alt="Offer" />
                            </div>
                            <div className='ml-sm'>
                                <div className='text-16'>
                                    Offer ID <span className='text-darkteal'>#{offerDetails?.offer_id}</span>
                                </div>
                                <div className='text-gray'>
                                    {dateFormatter(moment.utc(offerDetails?.start_date), moment.utc(offerDetails?.end_date), constantOptions?.DATE_FORMAT_Do_MM_YYYY)}
                                </div>
                            </div>
                        </div>
                        <div className='flex pt-sm'>
                            <div className='text-10 font-bold flex-center rounded-lg p-sm h-[22px] uppercase bg-tealTierBg text-tealTier' style={{ letterSpacing: '0.1em' }}>
                                {getTier(offerDetails?.lowest_visible_tier)}
                            </div>
                        </div>
                        <div className="flex items-center text-18 md:text-20">
                            <img className='mr-sm' src="/svg/golf-post-small.svg" />
                            {offerDetails?.club_name}
                        </div>

                        <div className='text-gray flex'>
                            <img width={14} src="/svg/location-grey.svg" height={10} style={{ marginRight: 5 }} />
                            {offerDetails?.club_address}
                        </div>
                        <div className='pb-sm'>
                            {offerDetails?.details}
                        </div>
                        <div className={`bg-lightestgray rounded-lg px-md py-sm flex flex-col ${offerDetails?.creator_id === user?.id ? 'cursor-default' : 'cursor-pointer'}`}
                            onClick={() => {
                                if (offerDetails?.creator_id === user?.id) {
                                    return
                                }
                                setOtherUserId(offerDetails?.creator_id)
                                setDrawer({
                                    type: drawer?.global ? GLOBAL_PROFILE_INFO : PROFILE,
                                    user: offerDetails?.creator_id,
                                    global: drawer?.global,
                                    source: 'map',
                                    customBackClickHandler: () => setDrawer({
                                        type: drawer?.type,
                                        offer: offerDetails,
                                        offerId: drawer?.offerId,
                                        clubId: drawer?.clubId,
                                        global: drawer?.global,
                                        customBackClickHandler: drawer?.customBackClickHandler,
                                        refresh: () => setRefreshOfferDetails(prev => prev + 1),
                                    })
                                })
                            }}
                        >
                            <div className='text-12 pb-sm'>Created By</div>
                            <div className="flex items-center relative">
                                {(offerDetails?.creator_profile_photo) ? (
                                    <div className='h-[40px] w-[40px] rounded-full mr-sm outline outline-1.5 outline-white'
                                        style={{
                                            backgroundImage: `url("${offerDetails?.creator_profile_photo}")`,
                                            backgroundSize: 'cover',
                                            backgroundPosition: 'center',
                                        }}
                                    ></div>
                                ) : (
                                    <NameInitials cssClassName={'mr-sm'} background={'bg-white'} rounded={'full'} height={40} width={40} user={{ first_name: offerDetails?.creator_name }} fontSize={16} />
                                )}
                                <div className="pl-sm">
                                    <div className="">
                                        {offerDetails?.creator_name}
                                    </div>
                                    <div className='flex items-center gap-2'>
                                        {offerDetails?.creator_is_tg_ambassador
                                            ? <div className='absolute top-[0px] left-[25px]'><AmbassadorTag smallTag={true} /></div>
                                            : null
                                        }
                                        {offerDetails?.creator_is_super_host
                                            ? <SuperHostTag />
                                            : null
                                        }
                                        {offerDetails?.creator_is_tg_founder ?
                                            <FcmTag />
                                            : null
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {(!isSelfRequest && !isSefClub) &&
                        <div className="absolute flex bottom-0 left-0 w-full px-md py-sm shadow-lg">
                            {(offer?.requested || offerDetails?.requested) ? (
                                <CustomButton
                                    text={"Requested"}
                                    onClick={() => { }}
                                    color='lighttealbackground2'
                                    textColor='darkteal'
                                    buttonImage='/svg/DarkTealTick.svg'
                                    imageMarginBottom='0px'
                                    width={"100%"}
                                    height={45}
                                />
                            ) : (
                                <CustomButton
                                    text={"Request Against Offer"}
                                    onClick={() => {
                                        if (maintenanceStatus?.request) {
                                            toastNotification({
                                                type: constantOptions.TOAST_TYPE.ERROR,
                                                message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                            })
                                        } else {
                                            handleRequest()
                                        }
                                    }}
                                    width={"100%"}
                                    height={45}
                                />
                            )}
                        </div>}

                    {offerDetails?.creator_id === user?.id &&
                        <div className='absolute flex bottom-0 left-0 w-full px-md py-sm shadow-lg'>
                            <CustomButton
                                onClick={() => {
                                    setModal({
                                        title: 'Delete Offer',
                                        width: 475,
                                        type: 'delete-offer',
                                        offer: offerDetails,
                                        refresh,
                                    })
                                }}
                                text={"Delete"}
                                textColor='black'
                                width={"100%"}
                                color='lightestgray'
                                buttonImage='/svg/DeleteBlack.svg'
                                imageMarginBottom='0px'
                            />
                            <CustomButton
                                imageMarginBottom='0px'
                                text={"Edit"}
                                textColor='black'
                                width={"100%"}
                                color='lightestgray'
                                buttonImage='/svg/EditBlack.svg'
                                onClick={(e) => {
                                    setModal({
                                        title: 'Edit Offer',
                                        img: {
                                            src: '/svg/offer-outline.svg',
                                            style: {
                                                height: 48,
                                                marginBottom: 10,
                                            },
                                        },
                                        width: 829,
                                        type: 'offer',
                                        offer: offerDetails,
                                        refresh: () => setRefreshOfferDetails(prev => prev + 1),
                                        source: offer?.my_tg_group_id?.length ? 'groups' : ''
                                    })
                                }}
                            />

                        </div>
                    }

                </div>
            }
        </div>
    )
}

export default OfferDetailsDrawer