import React, { useContext, useEffect, useState } from 'react';
import ENDPOINTS from '../../constants/endpoints.json';
import { UserContext } from '../../pages/_app';
import { ThreeDots } from 'react-loader-spinner';
import { DrawerContext } from '../../context/DrawerContext';
import OtherMemberCard from '../common/OtherMemberCard';
import constantOptions from '../../constants/constantOptions';
import ReadMore from '../../utils/truncate/readmore';
import { useRouter } from 'next/router';
import CustomButton from '../buttons/CustomButton';
import Link from 'next/link';
import NameInitials from '../common/NameInitials';
import useFriends from '../../hooks/chat-v2/useFriends';
import { CustomStreamContext } from '../../context/CustomStreamContext';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';

const GameInfoDrawer = ({ channel }) => {
  const router = useRouter();
  const { allFriendsId } = useFriends();
  const { token, user } = useContext(UserContext);
  const { drawer, setDrawer } = useContext(DrawerContext);
  const { setOtherUserId } = useContext(CustomStreamContext);
  const [showRequestorInfo, setShowRequestorInfo] = useState(true);
  const [showAdditionalInfo, setShowAdditionalInfo] = useState(true);
  const [showHostsInfo, setShowHostsInfo] = useState(true);
  const [showHostsList, setShowHostsList] = useState(true);
  const [showAcceptingHost, setShowAcceptingHost] = useState(true);
  const [gameInfo, setGameInfo] = useState({});
  const [userInfo, setUserInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [acceptingHost, setAcceptingHost] = useState({});
  const [acceptingHostUnreadMessages, setAcceptingHostUnreadMessages] =
    useState();
  const { isMobile } = useCheckDeviceScreen();
  const { GAME_INFO, GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE;
  const { setRequestId = () => { } } = drawer;
  const isRequestedAgainstOffer = !gameInfo?.english_fluency;
  const isReceivedAgainstOffer = !userInfo?.english_fluency;

  useEffect(() => {
    return () => {
      setRequestId('');

      router.push(
        {
          pathname: router?.pathname,
          search: `?type=${router?.query?.type}&tab=${router?.query?.tab}&subtype=${router?.query?.subtype}`,
        },
        undefined,
        { scroll: false, shallow: false },
      );
    };
  }, []);

  useEffect(() => {
    if (userInfo?.userId) {
      setAcceptingHost(
        drawer?.hosts?.filter((host) => host?.id === userInfo?.userId)[0],
      );
    }
  }, [userInfo]);

  useEffect(() => {
    if (acceptingHost?.stream_channel_id) {
      setAcceptingHostUnreadMessages(
        drawer?.streamChannels?.filter(
          (channel) => channel?.id === acceptingHost?.stream_channel_id,
        )[0]?.state?.unreadCount,
      );
    }
  }, [acceptingHost]);

  useEffect(() => {
    fetchDetails();
  }, [channel?.id]);

  const fetchDetails = async () => {
    try {
      setLoading(true);
      await fetch(
        `${drawer?.received ? ENDPOINTS.GET_RECEIVED_REQUEST_INFO : ENDPOINTS?.GET_REQUESTED_REQUEST_INFO}`,
        {
          method: 'POST',
          headers: {
            ['Content-Type']: 'application/json',
            ['Authorization']: `Bearer ${token}`,
          },
          body: JSON.stringify({
            userId: user?.id,
            requestId: drawer?.requestId,
          }),
        },
      )
        .then((res) => res.json())
        .then((data) => {
          setGameInfo(data?.data?.gameInfo);
          setUserInfo(data?.data?.userInfo);
          setAcceptingHost(
            drawer?.hosts?.filter(
              (host) => host?.id === data?.data?.userInfo?.userId,
            )[0],
          );
        });
    } catch (error) {
      console.log('------->', error);
    }
    setLoading(false);
  };

  return (
    <div
      className={`fade-in text-black flex flex-col shadow-lg allow-scroll bg-white`}
      style={{
        height: '100vh',
        width: isMobile ? '100%' : '500px',
        overflowY: 'scroll',
        position: 'fixed',
        top: 0,
        right: 0,
        zIndex: 9999,
        WebkitOverflowScrolling: 'touch', // For smooth scrolling on iOS
      }}
    >
      <div
        className="absolute cursor-pointer"
        style={{ right: 15, top: 22 }}
        onClick={() => {
          setDrawer();
        }}
      >
        <img src="/svg/CrossIconBlack.svg" />
      </div>
      <div className="text-21 font-medium flex-center bg-lightestgray py-md">
        Request #{drawer?.gameId || gameInfo?.game_id}
      </div>
      {loading ? (
        <div className="w-full flex-center h-full">
          <ThreeDots
            visible={true}
            height="50"
            width="50"
            color={'#098089'}
            radius="9"
            ariaLabel="three-dots-loading"
            wrapperStyle={{}}
            wrapperClass=""
          />
        </div>
      ) : (
        <div>
          <div className="text-18 text-darkteal mt-md bg-lightestgray pl-md py-sm">
            {drawer?.requested ? 'Request' : 'Game'} Information
          </div>
          <div className="px-md">
            <div className="text-gray pt-md">
              {!drawer?.requested ? 'Number Of People' : 'Club'}
            </div>
            <div className="text-16">
              {drawer?.requested
                ? gameInfo?.club_name
                : gameInfo?.number_of_players}
            </div>
            <div className="text-gray pt-md">
              {drawer?.isAcceptedRequest ? 'Game Date' : 'Requested Date(s)'}
            </div>
            <div className="text-16">{gameInfo?.date}</div>
            <div className="text-gray pt-md">Note</div>
            <div className="text-16 break-words pb-md">
              {!gameInfo?.message || gameInfo?.message.trim() === '' ? (
                '-'
              ) : gameInfo?.message && gameInfo?.message.length < 150 ? (
                gameInfo?.message
              ) : (
                <ReadMore className={'text-16'} length={150}>
                  {gameInfo?.message || ''}
                </ReadMore>
              )}
            </div>
            {/* <div className='text-16'>{gameInfo?.message}</div> */}
            {gameInfo?.accompanied_only ? (
              <div className="font-medium text-16 pb-md">
                Accompanied play only
              </div>
            ) : null}
          </div>

          {drawer?.received ? (
            <div className="flex-col">
              <div className="flex justify-between mt-md bg-lightestgray px-md py-sm">
                <div className="text-18 text-darkteal ">
                  Requester's Information
                </div>
                <img
                  className="cursor-pointer"
                  onClick={() => setShowRequestorInfo((prev) => !prev)}
                  src={`/svg/${showRequestorInfo ? 'down' : 'up'}-black.svg`}
                />
              </div>
              {drawer?.isFtr && userInfo?.deleted_at === null ? (
                <div className="text-12 font-medium flex bg-tealTierBg rounded-lg p-md mx-md my-sm">
                  <img src="/icons/ftr-flag.svg" />
                  <div className="pl-sm">
                    First Time Requestor:{' '}
                    <span className="font-normal">
                      This user is sending a request for the first time on
                      Thousand Greens.
                    </span>
                  </div>
                </div>
              ) : null}

              {showRequestorInfo ? (
                <OtherMemberCard
                  cssClassname="items-center"
                  height={74}
                  width={74}
                  fontSize={'16'}
                  subFontSize={'14'}
                  iconSize={14}
                  fetchTabsCount={drawer?.fetchTabsCount}
                  fetchUnreadMessageStatus={drawer?.fetchUnreadMessageStatus}
                  refresh={drawer?.refresh}
                  customBackClickHandler={() =>
                    setDrawer({
                      type: GAME_INFO,
                      global: true,
                      requestId: drawer?.requestId,
                      received: drawer?.received,
                      requested: drawer?.requested,
                      isFtr: drawer?.isFtr,
                    })
                  }
                  border={false}
                  member={{
                    id: userInfo?.userId,
                    profilePhoto: userInfo?.profilePhoto,
                    full_name: userInfo?.name,
                    email: userInfo?.email,
                    phone: userInfo?.phone,
                    isTgFounder: userInfo?.isTgFounder,
                    isSuperHost: userInfo?.isSuperHost,
                    tgAmbassadorVisible: userInfo?.tgAmbassadorVisibility,
                    isDeleted: userInfo?.deleted_at !== null,
                  }}
                />
              ) : null}
            </div>
          ) : null}

          {(drawer?.requested && !isRequestedAgainstOffer) ? (
            <div className='flex-col pb-sm'>
              <div className='flex justify-between bg-lightestgray px-md py-sm'>
                <div className='text-18 text-darkteal '>Additional Information</div>
                <img className='cursor-pointer' onClick={() => setShowHostsInfo((prev) => !prev)} src={`/svg/${showHostsInfo ? "down" : "up"}-black.svg`} />
              </div>
              {showHostsInfo ? (
                <div>
                  <div className='flex px-md'>
                    <div className='flex-1'>
                      <div className='text-gray pt-md'>Total Hosts</div>
                      <div className='text-16'>{gameInfo?.number_of_hosts}</div>
                    </div>
                    <div className='flex-1'>
                      <div className='text-gray pt-md'>Hosts Declined</div>
                      <div className='text-16 capitalize'>{drawer?.hostDeclinedCount}</div>
                    </div>
                  </div>
                  <div className='px-md pt-md'>
                    <div className='text-grayLight'>Request sent to members meeting the following criteria:</div>
                    <div className='flex'>
                      <div className='flex-1'>
                        <div className='text-gray pt-md'>Golf Index</div>
                        <div className='text-16'>{gameInfo?.handicap?.join(", ")}</div>
                      </div>
                      <div className='flex-1'>
                        <div className='text-gray pt-md'>English Fluency</div>
                        <div className='text-16 capitalize'>{gameInfo?.english_fluency?.join(", ")}</div>
                      </div>
                    </div>
                    <div className='flex'>
                      <div className='flex-1'>
                        <div className='text-gray pt-md'>Play As A Couple</div>
                        <div className='text-16'>{gameInfo?.playAsCouple ? "Yes" : "No"}</div>
                      </div>
                      <div className='flex-1'>
                        <div className='text-gray pt-md'>Age</div>
                        <div className='text-16 capitalize'>{gameInfo?.ageGroup ? "All" : `${gameInfo?.minAgeGroup} - ${gameInfo?.maxAgeGroup}`}</div>
                      </div>
                    </div>
                  </div>

                </div>
              ) : null}
            </div>
          ) : null}


          {drawer?.isAcceptedRequest && drawer?.isRequester && (
            <>
              <div className="flex-col mb-xs">
                <div className="flex justify-between bg-lightestgray px-md py-sm">
                  <div className="text-18 text-darkteal ">Accepting Host</div>
                  <img
                    className="cursor-pointer"
                    onClick={() => setShowAcceptingHost((prev) => !prev)}
                    src={`/svg/${showAcceptingHost ? 'down' : 'up'}-black.svg`}
                  />
                </div>
                {showAcceptingHost ? (
                  <div className="px-md">
                    <div className="py-md">
                      <div
                        className="flex items-center cursor-pointer rounded-lg bg-lightestgray px-sm py-sm justify-between"
                        onClick={() => {
                          setOtherUserId(acceptingHost?.id);
                          setDrawer({
                            type: GLOBAL_PROFILE_INFO,
                            global: true,
                            customBackClickHandler:
                              drawer?.customBackClickHandler,
                            fetchTabsCount: drawer?.fetchTabsCount,
                            fetchUnreadMessageStatus:
                              drawer?.fetchUnreadMessageStatus,
                            refresh: drawer?.refresh,
                          });
                        }}
                      >
                        <div className="flex items-center">
                          {acceptingHost?.profilePhoto ? (
                            <div
                              className="h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white"
                              style={{
                                backgroundImage: `url("${acceptingHost?.profilePhoto}")`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center',
                              }}
                            ></div>
                          ) : (
                            <NameInitials
                              cssClassName={'mr-sm'}
                              background={'bg-white'}
                              rounded={'full'}
                              height={24}
                              width={24}
                              user={{
                                first_name: allFriendsId?.includes(
                                  acceptingHost?.id,
                                )
                                  ? acceptingHost?.name
                                  : acceptingHost?.username,
                              }}
                              fontSize={16}
                            />
                          )}
                          <div className="flex-col">
                            <div className="text-[9px] text-darkteal px-xs py-[1px] rounded-full bg-tealTierBg inline-flex items-center justify-center">
                              HOST
                            </div>
                            <div>
                              {allFriendsId?.includes(acceptingHost?.id)
                                ? acceptingHost?.name
                                : acceptingHost?.username}
                            </div>
                          </div>
                        </div>
                        <Link
                          shallow={true}
                          href={`/dashboard/request${(!acceptingHost?.stream_channel_id && !acceptingHost?.sendbird_channel_id) || acceptingHost?.stream_channel_id ? '-chat' : ''}/[request_id]/[Host_id]`}
                          as={`/dashboard/request${(!acceptingHost?.stream_channel_id && !acceptingHost?.sendbird_channel_id) || acceptingHost?.stream_channel_id ? '-chat' : ''}/${drawer?.requestId}/${acceptingHost?.id}`}
                        >
                          <div className="relative">
                            {acceptingHostUnreadMessages ? (
                              <div
                                style={{ zIndex: 100 }}
                                className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"
                              ></div>
                            ) : null}

                            <CustomButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setDrawer();
                              }}
                              marginX="none"
                              height={30}
                              width={30}
                              imageRightMargin={0}
                              imageMarginBottom="0"
                              color="darkteal"
                              buttonImage="/svg/Message-Icon-White.svg"
                            />
                          </div>
                        </Link>
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>
            </>
          )}

          {drawer?.requested ? (
            <>
              {!drawer?.isAcceptedRequest ||
                (drawer?.isAcceptedRequest &&
                  drawer?.hosts?.filter((host) => host?.id !== userInfo?.userId)
                    ?.length) ? (
                <div className="flex-col mb-xs">
                  <div className="flex justify-between bg-lightestgray px-md py-sm">
                    <div className="text-18 text-darkteal ">
                      {drawer?.isAcceptedRequest ? 'Other Hosts' : `Hosts`}
                    </div>
                    <img
                      className="cursor-pointer"
                      onClick={() => setShowHostsList((prev) => !prev)}
                      src={`/svg/${showHostsList ? 'down' : 'up'}-black.svg`}
                    />
                  </div>
                  {showHostsList ? (
                    <div className="px-md">
                      {drawer?.hosts?.length ? (
                        <div className="py-md">
                          {(drawer?.isAcceptedRequest
                            ? drawer?.hosts?.filter(
                              (host) => host?.id !== userInfo?.userId,
                            )
                            : drawer?.hosts
                          )?.map((host) => {
                            const hasUnread = drawer?.streamChannels?.filter(
                              (channel) =>
                                channel?.id === host?.stream_channel_id,
                            )[0]?.state?.unreadCount;
                            return (
                              <div
                                className="flex h-[40px] items-center border-b border-lightestgray  py-lg px-sm justify-between cursor-pointer"
                                key={host?.id}
                                onClick={() => {
                                  if (host?.deleted_at === null) {
                                    setOtherUserId(host?.id);
                                    setDrawer({
                                      type: GLOBAL_PROFILE_INFO,
                                      global: true,
                                      customBackClickHandler:
                                        drawer?.customBackClickHandler,
                                      fetchTabsCount: drawer?.fetchTabsCount,
                                      fetchUnreadMessageStatus:
                                        drawer?.fetchUnreadMessageStatus,
                                      refresh: drawer?.refresh,
                                    });
                                  }
                                }}
                              >
                                {host?.deleted_at === null ? (
                                  <div className="flex items-center">
                                    {host?.profilePhoto ? (
                                      <div
                                        className="h-[24px] w-[24px] rounded-full mr-sm outline outline-1.5 outline-white"
                                        style={{
                                          backgroundImage: `url("${host?.profilePhoto}")`,
                                          backgroundSize: 'cover',
                                          backgroundPosition: 'center',
                                        }}
                                      ></div>
                                    ) : (
                                      <NameInitials
                                        cssClassName={'mr-sm'}
                                        background={'bg-lightestgray'}
                                        rounded={'full'}
                                        height={24}
                                        width={24}
                                        user={{
                                          first_name: allFriendsId?.includes(
                                            host?.id,
                                          )
                                            ? host?.name
                                            : host?.username,
                                        }}
                                        fontSize={16}
                                      />
                                    )}
                                    <div className="">
                                      {allFriendsId?.includes(host?.id)
                                        ? host?.name
                                        : host?.username}
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex items-center">
                                    <div className="h-[24px] w-[24px] rounded-full bg-lightestgray font-bold text-grayLight flex-center">
                                      ?
                                    </div>
                                    <div className="text-14 ml-md">
                                      Deleted User
                                    </div>
                                  </div>
                                )}
                                {host?.deleted_at === null && (
                                  <Link
                                    shallow={true}
                                    href={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || host?.stream_channel_id ? '-chat' : ''}/[request_id]/[host_id]`}
                                    as={`/dashboard/request${(!host?.stream_channel_id && !host?.sendbird_channel_id) || host?.stream_channel_id ? '-chat' : ''}/${drawer?.requestId}/${host?.id}`}
                                  >
                                    <div className="relative">
                                      {hasUnread ? (
                                        <div
                                          style={{ zIndex: 100 }}
                                          className="absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[0px] top-[0px] outline outline-1 outline-white"
                                        ></div>
                                      ) : null}

                                      <CustomButton
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setDrawer();
                                        }}
                                        marginX="none"
                                        height={30}
                                        width={30}
                                        imageRightMargin={0}
                                        imageMarginBottom="0"
                                        color="darkteal"
                                        buttonImage="/svg/Message-Icon-White.svg"
                                      />
                                    </div>
                                  </Link>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="flex items-center py-md">
                          <span className="h-[40px] w-[40px] rounded-full bg-tealTierBg flex-center mr-sm">
                            <img src="/icons/No-Hosts.svg" />
                          </span>
                          No hosts yet
                        </div>
                      )}
                    </div>
                  ) : null}
                </div>
              ) : null}
            </>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default GameInfoDrawer;
