import React, { useContext, useEffect, useState } from 'react'
import { ThreeDots } from 'react-loader-spinner'
import { UserContext } from '../../pages/_app';
import END_POINTS from '../../constants/endpoints.json';
import constantOptions from '../../constants/constantOptions';
import analyticsEventLog from '../../utils/firebase/analytics';
import useClient from '../../graphql/useClient';
import { UNFAVORITE_CLUB, FAVORITE_CLUB } from '../../graphql/mutations/club'
import { UPDATE_ADDITIONAL_SETTINGS, UPDATE_USER } from '../../graphql/mutations/user'
import router from 'next/router';
import { ModalContext } from '../../context/ModalContext';
import MESSAGES from '../../constants/messages';
import { DrawerContext } from '../../context/DrawerContext';
import CustomButton from '../buttons/CustomButton';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';
import fetchClubOffers from '../../utils/club/fetchClubOffers';
import fetchClubGameReviews from '../../utils/club/fetchClubGameReviews';
import ClubHeaderInfo from '../club/ClubHeaderInfo';
import ClubTgSection from '../club/ClubTgSection';
import ClubOffersSection from '../club/ClubOffersSection';
import ClubReviewSection from '../club/ClubReviewSection';
import setClevertapUser from '../../utils/clevertap/setClevertapUser';
import isWithinRadius from '../../utils/helper/isWithinRadius';
import { useCurrentLocation } from '../../hooks/useCurrentLocation';
import toastNotification from '../../utils/notifications/toastNotification';
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus';
const { MAP_TOTAL_TG_GROUP_MEMBER, MAP_FRIENDS_PLAYED, MAP_ALL_MY_FRIENDS } = constantOptions?.DRAWER_TYPE
const { ALL_GAME_REVIEWS } = constantOptions?.DRAWER_TYPE
const { CLEVERTAP_EVENTS } = constantOptions



const CLUB_DETAILS_LISTS = [
    {
        name: "TG Group Members",
        icon: "/svg/Total-Members.svg",
        key: "tgGroupMembersCount",
        drawerType: MAP_TOTAL_TG_GROUP_MEMBER,
        eventName: constantOptions?.CLEVERTAP_EVENTS?.MAP_TOTAL_TG_GROUP_MEMBER
    },
    {
        name: "Friends Played",
        icon: "/svg/Friends-Played.svg",
        key: "friendsPlayed",
        drawerType: MAP_FRIENDS_PLAYED,
        eventName: constantOptions?.CLEVERTAP_EVENTS?.MAP_FRIENDS_PLAYED
    },
    {
        name: "Total Friends",
        icon: "/svg/Total-Friends.svg",
        key: "friends",
        drawerType: MAP_ALL_MY_FRIENDS,
        eventName: constantOptions?.CLEVERTAP_EVENTS?.MAP_ALL_MY_FRIENDS
    },
    {
        name: "Total Contacts",
        icon: "/svg/TotalContacts.svg",
        key: "contacts",
    },
]

const ClubDetailsDrawer = ({ }) => {
    const { setModal } = useContext(ModalContext)
    const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
    const [played, setPlayed] = useState(user?.playedClubs?.includes(selectedClub.id))
    const [favorited, setFavorited] = useState(user && user?.favorite_clubs.filter(({ club_id }) => club_id === selectedClub.id).length === 1)
    const { user, fetchUser, token, clubForRequest, setClubForRequest, clevertap } = useContext(UserContext)
    const [club, setClub] = useState(null);
    const [isShowLoading, setIsShowLoading] = useState(true);
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [contactOnlyClub, setContactOnlyClub] = useState(false)
    const client = useClient();
    const userClubIds = user?.clubs?.map(club => club?.id)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
    const { setClubsRecommended = () => { }, setActiveTab = () => { } } = drawer
    const { isMobile, isTablet, isDesktop, isWideScreen } = useCheckDeviceScreen()
    const [refreshOfferDetails, setRefreshOfferDetails] = useState(0)
    const { lat, lng } = useCurrentLocation()
    const { maintenanceStatus } = useMaintenanceStatus()


    const {
        setIsMapInfoShow = () => { },
        selectedClub,
        filterActive,
        offerRestricted,
        setShowCreateRequestForm,
        dataForClub,
        setDataForClub,
        setRefreshMap = () => { }
    } = drawer

    const [offers, setOffers] = useState([])
    const [offersCount, setOffersCount] = useState(false)
    const [gameReviews, setGameReviews] = useState([])
    const [reveiewsCount, setReviewsCount] = useState([])

    useEffect(() => {
        fetchGameReviews()
        fetchOffers()

    }, [selectedClub, refreshOfferDetails])

    const fetchGameReviews = async () => {
        try {
            const result = await fetchClubGameReviews({
                token,
                userId: user?.id,
                clubId: selectedClub?.id,
                page: 0,
                limit: 3
            });

            setGameReviews(result.reviews);
            setReviewsCount(result.reviewsCount);
        } catch (error) {
            console.log('Error in fetchGameReviews:', error);
        }
    }

    const fetchOffers = async () => {
        try {
            const result = await fetchClubOffers({
                token,
                userId: user?.id,
                clubId: selectedClub?.id,
                page: 0,
                limit: 3
            });

            setOffers(result.offers);
            setOffersCount(result.offersCount);
        } catch (error) {
            console.log('Error in fetchOffers:', error);
        }
    }

    const isShowCreateRequest = () => {
        if (selectedClub?.color?.includes("blue")) {
            if (user?.private_network?.id) {
                return !!((club?.pnCount || 0) + club?.isEligibleForCreateRequest)
            } else {
                return !!(club?.isEligibleForCreateRequest)
            }

        } else if (!['grey', 'grey_contact', 'teal', 'teal_contact'].includes(selectedClub?.color)) {
            return true
        }
        return false
    }
    const canCreateRequest = isShowCreateRequest() && !contactOnlyClub


    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
        }
    }, [user])

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
        }
    }, [user])

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])


    useEffect(() => {
        setFavorited((
            user && user?.favorite_clubs.filter(({ club_id }) => club_id === selectedClub?.id).length === 1
        ));
        setIsShowLoading(true);
        getClubInfo();
        checkIfCreateRequest();
    }, [selectedClub, dataForClub, refreshOfferDetails])

    const checkIfCreateRequest = async () => {
        fetch(END_POINTS.CREATE_REQUEST_TOKEN_CHECK, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: user.id,
            }),
        }).then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null);
                } else {
                    setRequestRestricted(data.message);
                }
            })
            .catch(err => console.error('Error', err));
    }

    const getClubInfo = async () => {
        if (selectedClub?.id || dataForClub?.clubId) {
            fetch(END_POINTS.FETCH_CLUB_DETAIL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user.id,
                    clubId: selectedClub.id || dataForClub?.clubId,
                    clubColor: selectedClub?.color || dataForClub?.clubColor
                }),
            }).then((data) => data.json())
                .then((data) => {
                    setClub(data);
                    setIsShowLoading(false);
                    setPlayed(data?.userPlayedAtClub)
                    //Check if the club is a contact only Club

                    if (!userClubIds?.includes(data?.clubs?.id) // if the user is not a part of this club
                        && (data?.contacts || data?.friends) // and the user has contacts or friends
                        && (
                            (!user?.visibleToPublic && selectedClub?.color === "teal_contact") // this means it is a MY TG club
                            || !data?.clubMemberCount // and there is no member in this club
                        )
                    ) {
                        setContactOnlyClub(true)
                    } else {
                        setContactOnlyClub(false)
                    }
                    // if (Object.entries(dataForClub).length !== 0) {
                    //     window?.sessionStorage.removeItem("MAP_LIST_TYPE")
                    //     // setDataForClub({})
                    // }
                })
        } else {
            setClub(null)
        }
    }

    const isShowSeeOffer = () => {
        if (['blue', 'blue_contact'].includes(selectedClub?.color)) {
            return true
        }
        return false
    }

    const isShowCreateOffer = () => {
        return (userClubIds?.includes(selectedClub?.id) && selectedClub?.ct !== constantOptions?.CLUB_TYPES?.VIRTUAL)
    }

    const setClubFavourite = () => {
        const user_id = user.id
        const club_id = selectedClub.id
        setFavorited(!favorited);
        if (favorited) {
            client
                .request(UNFAVORITE_CLUB, { user_id, club_id })
                .then(() => {
                    fetchUser();
                })
        } else {
            client
                .request(FAVORITE_CLUB, { user_id, club_id })
                .then(() => {
                    fetchUser();
                })
        }
        drawer?.setRefreshMap(prev => prev + 1)
    }

    const setClubPlayed = () => {
        setPlayed(!played);
        if (played) {
            client.request(UPDATE_USER, {
                user_id: user.id,
                user: {
                    playedClubs: user.playedClubs.filter(
                        (id) => id !== selectedClub.id
                    ),
                },
            })
                .then(() => {
                    fetchUser();
                })
        } else {
            client.request(UPDATE_USER, {
                user_id: user.id,
                user: {
                    playedClubs: [...user.playedClubs, selectedClub.id],
                },
            })
                .then(() => {
                    analyticsEventLog(constantOptions.USER_ACTIVITY.MAP_PLAYED.event, {
                        id: Math.random(),
                        userId: user?.id,
                        from: constantOptions.USER_ACTIVITY.MAP_PLAYED.from
                    })
                    fetchUser();
                })
        }
        drawer?.setRefreshMap(prev => prev + 1)
    }

    const fetchClubSearchDetail = async () => {
        setIsShowLoading(true);
        try {
            fetch(END_POINTS.SEARCH_CLUB,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        searchValue: selectedClub?.name,
                        userId: user?.id,
                        exactMatch: true
                    }),
                })
                .then((data) => data.json())
                .then(({ clubs }) => {
                    const clubDetail = clubs[0]
                    setIsMapInfoShow(false)
                    setClubForRequest(clubDetail)
                    setIsShowLoading(false);
                    setActiveTab('create-request')
                    // sessionStorage.setItem('selected_club_for_offers', JSON.stringify(selectedClub?.id))
                    router.push({
                        pathname: '/dashboard/play',
                        search: `?type=create-request`
                    })
                })
        } catch (error) {
            console.error('fetchClubSearchDetail --->', error);
        }
    }

    const openCreateOfferModal = () => {
        //Checking of the club is muted
        let isClubMuted = false
        user?.clubs.map(club => {
            if (club?.id === selectedClub?.id && club?.muted) {
                isClubMuted = true
            }
        })

        if (isClubMuted) {
            setModal({
                title: 'Offer Limit',
                message: MESSAGES['can_create_offer']['CLUB_IS_MUTED'],
                type: 'warning',
            })
        }
        else {
            if (offerRestricted) {
                setModal({
                    title: 'Offer Limit',
                    message: offerRestricted,
                    type: 'warning',
                })
            } else {
                setModal({
                    title: 'Create Offer',
                    img: {
                        src: '/svg/offer-outline.svg',
                        style: { height: 48, marginBottom: 10 },
                    },
                    width: 829,
                    type: 'offer',
                    club: club?.clubs,
                    refresh: setRefreshOfferDetails
                })
            }
        }
    }

    let mapInfoReturnData = JSON.parse(window?.sessionStorage.getItem('MAP_INFO_RETURN_DATA'))

    const openTgGroupsList = () => {
        setDrawer({
            type: MAP_TOTAL_TG_GROUP_MEMBER,
            club: club?.clubs,
            clubColor: selectedClub?.color,
            returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
            canCreateRequest,
            source: "map-info-drawer",
            customBackClickHandler: openTgGroupsList,
        })
    }

    const openFriendsPlayedDrawer = () => {
        setDrawer({
            type: MAP_FRIENDS_PLAYED,
            club: club?.clubs,
            clubColor: selectedClub?.color,
            returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
            canCreateRequest,
            source: "map-info-drawer",
            customBackClickHandler: openFriendsPlayedDrawer,
        })
    }

    const openTotalFriendsDrawer = () => {
        setDrawer({
            type: MAP_ALL_MY_FRIENDS,
            club: club?.clubs,
            clubColor: selectedClub?.color,
            returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
            source: "map-info-drawer",
            canCreateRequest,
            customBackClickHandler: openTotalFriendsDrawer
        })
    }

    const handleOfferClick = (offer, eventName) => {
        setClevertapUser(clevertap, user);
        clevertap.event.push(eventName);
        clevertap.profile.push({
            "Site": {
                [constantOptions?.CLEVERTAP.OFFER_ID]: offer?.offer_id,
                [constantOptions?.CLEVERTAP.OFFER_CREATED_FOR]: offer?.my_tg_group_id?.length ? 'My TG' : 'All',
                [constantOptions?.CLEVERTAP.LOCATOR_COLOUR]: selectedClub?.color || dataForClub?.clubColor,
                [constantOptions?.CLEVERTAP.CLUB_NAME]: offer?.club_name,
                [constantOptions?.CLEVERTAP.CLUB_TIER]: offer?.lowest_visible_tier,
                [constantOptions?.CLEVERTAP.CLUB_TYPE]: club?.clubs?.club_type === constantOptions?.CLUB_DEMAND_TYPE?.OVER_REQUESTED
                    ? 'Highly Requested'
                    : 'Unexplored',
                [constantOptions?.CLEVERTAP.CLUB_ADDRESS]: offer?.club_address,
                [constantOptions?.CLEVERTAP.USER_EMAIL]: user?.email,
                [constantOptions?.CLEVERTAP.USER_TIER]: user?.tier,
                [constantOptions?.CLEVERTAP.USER_MEMBERSHIP]: user?.membership_active,
                [constantOptions?.CLEVERTAP.USER_CITY]: user?.stripe_customer_info?.address?.city,
                [constantOptions?.CLEVERTAP.USER_STATE]: user?.stripe_customer_info?.address?.state,
                [constantOptions?.CLEVERTAP.USER_COUNTRY]: user?.stripe_customer_info?.address?.country,
                [constantOptions?.CLEVERTAP.CLUB_IN_CURRENT_LOCATION]: isWithinRadius(club?.clubs?.lat, club?.clubs?.lng, lat, lng, 100) ? 'Yes' : 'No'

            }
        });
    }
    const handleCreateRequestClick = (club) => {
        setClevertapUser(clevertap, user);
        clevertap.event.push(CLEVERTAP_EVENTS?.CLICK_ON_CREATE_REQUEST_FROM_MAP);
        clevertap.profile.push({
            "Site": {
                [constantOptions?.CLEVERTAP.CLUB_NAME]: club?.clubs?.name,
                [constantOptions?.CLEVERTAP.CLUB_TIER]: club?.clubs?.lowest_visible_tier,
                [constantOptions?.CLEVERTAP.CLUB_ADDRESS]: club?.clubs?.address,
                [constantOptions?.CLEVERTAP.USER_EMAIL]: user?.email,
                [constantOptions?.CLEVERTAP.USER_TIER]: user?.tier,
                [constantOptions?.CLEVERTAP.USER_MEMBERSHIP]: user?.membership_active,
                [constantOptions?.CLEVERTAP.USER_CITY]: user?.stripe_customer_info?.address?.city,
                [constantOptions?.CLEVERTAP.USER_STATE]: user?.stripe_customer_info?.address?.state,
                //FCM, TGA, Super Host, Super Guest
                [constantOptions?.CLEVERTAP.USER_TAG]: `${user?.is_tg_ambassador && 'TGA'}, ${user?.is_tg_founder && 'FCM'}, ${user?.is_super_host && 'Super Host'}, ${user?.is_super_guest && 'Super Guest'}`
            }
        });
    }
    return (
        <div
            id="map-drawer-clubs"
            className='bottom-0 right-0 h-full'
            style={{
                width: (isDesktop || isWideScreen) ? '500px' : isTablet ? '69%' : '100%',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.14)',
                top: (searchActive) ? 48 : 0,
                zIndex: 2
            }}>
            <div className="relative bg-white w-full h-full flex flex-col items-center">
                {isShowLoading && (
                    <div className='absolute w-full flex-center h-full bg-white ' style={{ zIndex: 99999 }}>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                )}
                {club !== null && (
                    <div
                        className="pl-[2px] absolute overflow-scroll w-full"
                        style={{
                            bottom: 45, // Add space for the button at bottom
                            top: 0,
                            overflowX: 'hidden'
                        }}>
                        <div>
                            <div className='text-18 md:text-20 h-[58px] flex-center bg-lightestgray relative'>Club Details
                                <div
                                    className='cursor-pointer absolute right-[20px] top-[24px]'
                                    onClick={() => {
                                        setDrawer()
                                        setRefreshMap(prev => prev + 1)
                                    }}>
                                    <img width={12} src="/svg/CrossIconBlack.svg" />
                                </div>
                                {/* Club Details */}

                            </div>
                            <ClubHeaderInfo
                                club={club}
                                played={played}
                                favorited={favorited}
                                setPlayed={setPlayed}
                                setClubPlayed={setClubPlayed}
                                setFavorited={setFavorited}
                                setClubFavourite={setClubFavourite}
                                contactOnlyClub={contactOnlyClub}
                                userClubIds={userClubIds}
                            />
                            {club['tgGroupMembersCount'] === 0 && club['friendsPlayed'] === 0 && club['friends'] === 0 && club['contacts'] === 0 ? null :
                                <div className="h-[12px] bg-lightestgray"></div>
                            }
                            {club['tgGroupMembersCount'] === 0 && club['friendsPlayed'] === 0 && club['friends'] === 0 && club['contacts'] === 0 ? null :
                                <>
                                    <ClubTgSection
                                        club={club}
                                        clubDetailsList={CLUB_DETAILS_LISTS}
                                        openTgGroupsList={openTgGroupsList}
                                        openFriendsPlayedDrawer={openFriendsPlayedDrawer}
                                        openTotalFriendsDrawer={openTotalFriendsDrawer}
                                        mapConstants={{
                                            MAP_TOTAL_TG_GROUP_MEMBER,
                                            MAP_FRIENDS_PLAYED,
                                            MAP_ALL_MY_FRIENDS
                                        }}
                                    />
                                    <div className="h-[12px] bg-lightestgray"></div>
                                </>
                            }

                            {(offers?.length && isShowSeeOffer) ?
                                <>
                                    <ClubOffersSection
                                        handleOfferClick={handleOfferClick}
                                        offers={offers}
                                        offersCount={offersCount}
                                        user={user}
                                        requestRestricted={requestRestricted}
                                        showCreateRequestPopup={showCreateRequestPopup}
                                        setShowCreateRequestPopup={setShowCreateRequestPopup}
                                        setModal={setModal}
                                        setRefreshOfferDetails={setRefreshOfferDetails}
                                        customBackClickHandler={() => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest })}
                                        onViewAllClick={() => {
                                            setDrawer()
                                            router.push({
                                                pathname: '/dashboard/offers',
                                            })
                                            sessionStorage.setItem('selected_club_for_offers', JSON.stringify(selectedClub?.id))
                                        }}
                                    />
                                </> : ''
                            }
                            {gameReviews?.length ?
                                <ClubReviewSection
                                    reviews={gameReviews}
                                    reviewsCount={reveiewsCount}
                                    user={user}
                                    customBackClickHandler={() => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest })}
                                    onViewAllClick={() => {
                                        setDrawer({
                                            type: ALL_GAME_REVIEWS,
                                            club: club?.clubs,
                                            source: "map-info-drawer",
                                            selectedClub,
                                            returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest }),
                                            customBackClickHandler: () => setDrawer({
                                                type: ALL_GAME_REVIEWS,
                                                club: club?.clubs,
                                                source: "map-info-drawer",
                                                selectedClub,
                                                returnToMapInfo: () => setDrawer({ ...mapInfoReturnData, setActiveTab, setClubsRecommended, setClubForRequest })
                                            })
                                        })
                                    }}
                                />
                                : null}
                        </div>
                    </div>
                )
                }

                {/* Fixed at bottom */}
                <div className="absolute flex bottom-0 left-0 w-full px-md py-sm shadow-lg">
                    {(canCreateRequest && !contactOnlyClub) ? (
                        <CustomButton
                            height={45} width={"100%"}
                            text='Create Request'
                            buttonImage='/markers/create-request.svg'
                            marginX="none"
                            onClick={() => {
                                if (maintenanceStatus?.request) {
                                    toastNotification({
                                        type: constantOptions.TOAST_TYPE.ERROR,
                                        message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                    })
                                } else {
                                    handleCreateRequestClick(club)
                                    if (requestRestricted) {
                                        setModal({
                                            title: 'Request Limit',
                                            message: requestRestricted,
                                            type: 'warning',
                                        })
                                    } else {
                                        setClubsRecommended(false)
                                        fetchClubSearchDetail()
                                    }
                                }
                            }}
                        />
                    ) : ''}
                    {isShowCreateOffer() ? (
                        <CustomButton
                            height={45} width={"100%"}
                            text='Create Offer'
                            buttonImage='/svg/offer.svg'
                            buttonImageWidth={25}
                            imageMarginBottom='0'
                            onClick={() => {
                                if (maintenanceStatus?.request) {
                                    toastNotification({
                                        type: constantOptions.TOAST_TYPE.ERROR,
                                        message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                    })
                                } else {
                                    openCreateOfferModal()
                                }
                            }}
                        />
                    ) : ''}
                </div>
            </div>
        </div>
    )
}

export default ClubDetailsDrawer
