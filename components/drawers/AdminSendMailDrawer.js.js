import React, { useContext, useEffect, useState } from 'react'
import XIcon from '../icons/RequestV2/XIcon'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import { ThreeDots } from 'react-loader-spinner'
import CustomAvatar from '../chat-v2/CustomAvatar'
import { UserContext } from '../../pages/_app';
import ENDPOINTS from "../../constants/endpoints.json";
import constantOptions from '../../constants/constantOptions'
import { ModalContext } from '../../context/ModalContext'
import { CustomStreamContext } from '../../context/CustomStreamContext'
import { RichTextEditor, TextArea } from '../common'
import CustomButton from '../buttons/CustomButton'
import toastNotification from '../../utils/notifications/toastNotification'

const AdminSendMailDrawer = ({ drawer, setDrawer }) => {

    const { isMobile } = useCheckDeviceScreen();
    const { token } = useContext(UserContext)
    const { setModal } = useContext(ModalContext)
    const [loading, setLoading] = useState(false)
    const [message, setMessage] = useState('')
    const [content, setContent] = useState('')

    const handleSendMail = async () => {
        setLoading(true)
        const response = await fetch(ENDPOINTS.ADMIN_BROADCAST_EMAIL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                subject: message,
                body: content
            })
        })
        if (response.ok) {
            toastNotification({
                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                message: "Email has been successfully sent",
            })
        } else {
            toastNotification({
                type: constantOptions.TOAST_TYPE.ERROR,
                message: "Failed to send email",
            })
        }
        setLoading(false)
        setModal()
        setDrawer()
    }

    return (
        <div
            className='fade-in bg-white shadow-lg allow-scroll relative'
            style={{
                width: isMobile ? "100%" : "500px",
                height: '100vh',
                overflowY: 'scroll',
                position: 'fixed',
                top: 0,
                right: 0,
                zIndex: 9999,
                WebkitOverflowScrolling: 'touch'
            }}
        >
            <div className='bg-lightestgray flex justify-between py-lm flex-center relative'>
                <div className='text-21 font-medium'>Send an Email to Active Users</div>
                <div
                    onClick={() => setDrawer()}
                    className="cursor-pointer absolute right-[20px] top-[28px]">
                    <img src='/svg/CrossIconBlack.svg' />
                </div>
            </div>

            <div className='flex flex-col px-md py-md'>
                <TextArea
                    titleSize={'text-14'}
                    titleColor={'text-grayLight'}
                    title={'Subject'}
                    disableError={''}
                    error={''}
                    placeholder={"Type the note here"}
                    value={message}
                    className={' rounded-lg px-md mb-md'}
                    maxLength={500}
                    minHeight={100}
                    fontSize={"text-12"}
                    update={(val) => {
                        setMessage(val)
                    }} />

                <div>
                    <div className='text-14 text-grayLight font-normal mb-sm'>Message</div>
                    <RichTextEditor
                        editorHtml={content}
                        updateEditorHtml={(value) => {
                            setContent(value)
                        }}
                        modules={{
                            toolbar: [['bold', 'italic', 'underline'], [{ 'list': 'ordered' }, { 'list': 'bullet' }], ['clean']],
                            clipboard: {
                                matchVisual: false,
                            },
                        }}
                        formats={['bold', 'italic', 'underline', 'list']}
                        placeholder={"Enter Message here..."}
                    />
                </div>

                <div className='flex justify-between items-center absolute bottom-0 left-0 right-0 py-md px-md bg-white shadow-lg'>
                    <CustomButton
                        loading={loading}
                        text={'Cancel'}
                        onClick={() => setDrawer()}
                        width={'100%'}
                        textColor={'text-black'}
                        color={'lightestgray'}
                    />
                    <CustomButton
                        loading={loading}
                        text={'Send Mail'}
                        onClick={()=>{
                            setModal({
                                type: "confirmation-modal",
                                text: "Are you sure you want to send this E-mail to All Active TG users?",
                                buttonColor: "darkteal",
                                onClick: handleSendMail,
                            })
                        }}
                        width={'100%'}
                        disabled={(message && content && content !== '<p><br></p>') ? false : true}
                    />
                </div>
            </div>
        </div>
    )
}

export default AdminSendMailDrawer