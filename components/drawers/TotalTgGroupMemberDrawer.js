import React, { useEffect, useState, useContext } from 'react'
import { MapAllMyFriendsListItem } from '../clubs/components/MapAllMyFriendsListItem'
import CustomButton from '../buttons/CustomButton'
import { ThreeDots } from 'react-loader-spinner'
import PulseSkeleton from '../common/PulseSkeleton'
import ENDPOINTS from "../../constants/endpoints.json"
import InfiniteScroll from 'react-infinite-scroll-component'
import { UserContext } from '../../pages/_app'
import { ModalContext } from '../../context/ModalContext'
import useFriends from '../../hooks/chat-v2/useFriends'
import useClient from '../../graphql/useClient'
import { UPDATE_ADDITIONAL_SETTINGS } from '../../graphql/mutations/user'
import { DrawerContext } from '../../context/DrawerContext'
import useDrawerHeightCalculator from '../../hooks/useDrawerHeightCalculator'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'


export const TotalTgGroupMemberDrawer = ({ }) => {

    const { myFriends } = useFriends()
    const [page, setPage] = useState(1)
    const [loading, setLoading] = useState(false)
    const [totalCount, setTotalCount] = useState(0)
    const { token, user, fetchUser } = useContext(UserContext)
    const [groupMembers, setGroupMembers] = useState([])
    const { modal, setModal } = useContext(ModalContext)
    const [allFriendsId, setAllfriendsId] = useState([])
    const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT
    const [requestRestricted, setRequestRestricted] = useState(true)
    const [showAcceptRequestPopup, setShowAcceptRequestPopup] = useState();
    const [showCreateRequestPopup, setShowCreateRequestPopup] = useState();
    const client = useClient()
    const isSelfclub = user?.clubs?.filter((c) => c?.id === club?.id)
    const [mutedGroupMembers, setMutedGroupMembers] = useState(0)
    const disableRequestToAll = mutedGroupMembers === groupMembers?.length
    const [genderCompability, setGenderCompability] = useState(false)
    const { drawer, setDrawer, searchActive } = useContext(DrawerContext)
    const { club, clubColor } = drawer
    const { drawerHeight } = useDrawerHeightCalculator(searchActive)
    const { isDesktop, isWideScreen, isTablet } = useCheckDeviceScreen()
    const { maintenanceStatus } = useMaintenanceStatus()

    const MAP_CAN_CREATE_REQUEST = window?.sessionStorage?.getItem("MAP_CAN_CREATE_REQUEST")


    useEffect(() => {
        fetchTgGroupMembers()
        checkToken()
    }, [])

    useEffect(() => {
        if (groupMembers?.length) {
            const count = groupMembers.filter((member) => member?.isMuted)
            setMutedGroupMembers(count?.length);
        }
    }, [groupMembers])

    useEffect(() => {
        const uniqueArrayOfAllFriends = myFriends.map((friend) => {
            return friend?.friend_id;
        });
        setAllfriendsId(uniqueArrayOfAllFriends)
    }, [myFriends])

    useEffect(() => {
        if (user) {
            setShowAcceptRequestPopup(user?.additional_settings?.showAcceptRequestPopup);
            setShowCreateRequestPopup(user?.additional_settings?.showCreateRequestPopup);
        }
    }, [user])

    useEffect(() => {
        if (user, client) {
            if (user?.additional_settings?.showAcceptRequestPopup && showAcceptRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showAcceptRequestPopup: false
                    }
                })
                fetchUser()
            }
            if (user?.additional_settings?.showCreateRequestPopup && showCreateRequestPopup === false) {
                client.request(UPDATE_ADDITIONAL_SETTINGS, {
                    user_id: user?.id,
                    additional_settings: {
                        ...user?.additional_settings,
                        showCreateRequestPopup: false
                    }
                })
                fetchUser()
            }
        }
    }, [showAcceptRequestPopup, showCreateRequestPopup, user, client])

    async function checkToken() {
        fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: user.id
            }),
        })
            .then((data) => data.json())
            .then((data) => {
                if (data.canCreate) {
                    setRequestRestricted(null);
                } else {
                    setRequestRestricted(data.message);
                }
            })
            .catch(err => console.log('Error'));
    }

    const fetchTgGroupMembers = async (pageToLoad = page) => {
        try {
            setLoading(true)
            await fetch(ENDPOINTS.FETCH_TG_GROUP_MEMBER_LIST, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    clubId: club?.id,
                    userId: user?.id,
                    page: pageToLoad,
                    limit: pageSize,
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    setGenderCompability(data?.members?.map((member) => member?.isGenderCompatible).includes(false))
                    if (pageToLoad !== 1) {
                        setGroupMembers((prev) => [...prev, ...data?.members])
                        setTotalCount(data?.totalCount)
                    } else {
                        setGroupMembers(data?.members)
                        setTotalCount(data?.totalCount)
                    }

                })
            setPage(pageToLoad)
            setLoading(false)
        } catch (error) {
            console.log("Map All My friends list----", error);
        }
    }

    const handleCreateRequest = (requestType, friendId) => {
        if (requestRestricted) {
            setModal({
                title: 'Request Limit',
                message: requestRestricted,
                type: 'warning',
            })
        } else {
            if (showCreateRequestPopup) {
                setModal({
                    requestType: requestType,
                    club,
                    type: 'request-confirmation',
                    width: 500,
                    friendId,
                    setShowCreateRequestPopup: (val) => {
                        setShowCreateRequestPopup(val)
                    },
                })
            } else {
                setModal({
                    type: 'request-against-offer',
                    title: 'Create Request',
                    img: {
                        src: '/svg/request-offer.svg',
                        style: {
                            height: 80,
                            marginBottom: 10,
                        },
                    },
                    club,
                    friendId,
                    requestType: requestType
                })
            }
        }
    }

    return (
        <div
            id="map-drawer-clubs"
            className='bottom-0 right-0 h-full'
            style={{
                width: (isDesktop || isWideScreen || isTablet) ? '500px' : '100%',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.14)',
                zIndex: 2
            }}>
            <div className="relative bg-white w-full h-full flex flex-col items-center">
                {loading && (
                    <div className='absolute w-full flex-center h-full ' style={{ zIndex: 99999 }}>
                        <ThreeDots
                            visible={true}
                            height="50"
                            width="50"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                )}
                <div
                    className="pl-[2px] absolute w-full"
                    style={{
                        bottom: 45, // Add space for the button at bottom
                        top: 0,
                        overflowX: 'hidden'
                    }}>
                    <div className={`flex justify-between flex-center relative bg-lightestgray min-h-[58px]`} >
                        {drawer?.customBackClickHandler &&
                            <div className='flex text-16 font-medium absolute cursor-pointer text-gray'
                                style={{ left: 10 }}
                                onClick={() => {
                                    // drawer?.customBackClickHandler()
                                    drawer?.returnToMapInfo()
                                }}
                            >
                                <img width={15}
                                    style={{ transform: 'rotate(90deg)', marginRight: 10 }}
                                    src='/svg/arrow-down.svg' />Back
                            </div>
                        }
                        <div className='text-18 md:text-20 font-medium h-[58px] flex-center bg-lightestgray relative'>TG Group Members</div>
                        <div
                            onClick={() => {
                                setDrawer()
                            }}
                            className="cursor-pointer absolute" style={{ right: 20, top: 20 }}>
                            <img src='/svg/CrossIconBlack.svg' />
                        </div>
                    </div>
                    <div className='w-full flex-1'>
                        <InfiniteScroll
                            height={(window.innerHeight - 220)}
                            dataLength={groupMembers?.length}
                            next={() => fetchTgGroupMembers((page + 1))}
                            hasMore={groupMembers?.length === pageSize * page}
                            loader={<CustomLoader />}
                            scrollThreshold={0.8}
                        >
                            {groupMembers?.map((friend) => (
                                <MapAllMyFriendsListItem
                                    key={friend?.id}
                                    friend={{
                                        friend_info: {
                                            name: friend?.fullName,
                                            profilePhoto: friend?.profilePhoto,
                                            username: friend?.username
                                        },
                                        friend_id: friend?.id,
                                        isFriend: allFriendsId.includes(friend?.id),
                                        isMuted: friend?.isMuted,
                                        is_muted: friend?.isMuted,
                                        groupNames: friend?.groupNames
                                    }}
                                    club={club}
                                    totalTgGroupMembers={true}
                                    requestType={"MY_TG_GROUP_MEMBER"}
                                    handleCreateRequest={handleCreateRequest}
                                    clubColor={clubColor}
                                    canCreateRequest={drawer?.canCreateRequest || MAP_CAN_CREATE_REQUEST}
                                    isGenderCompatible={friend?.isGenderCompatible}
                                    customBackClickHandler={drawer?.customBackClickHandler}
                                />
                            ))}
                        </InfiniteScroll>
                    </div>
                </div>
                <div className={`absolute flex bottom-0 left-0 py-sm shadow-lg bg-white py-sm ${!isSelfclub.length ? ' w-full justify-evenly' : 'justify-center'}`}>
                    <CustomButton
                        height={45}
                        width={"100%"}
                        text='Send Message to all'
                        buttonImage='/svg/SendMessageTiltedWhite.svg'
                        onClick={() => {
                            setModal({
                                type: "broadcast-message",
                                source: "TOTAL_TG_MEMBERS_LIST",
                                clubId: club?.id
                            })
                        }}
                    />
                    {(!isSelfclub?.length && (drawer?.canCreateRequest || MAP_CAN_CREATE_REQUEST) && !disableRequestToAll && !genderCompability) &&
                        <CustomButton
                            height={45}
                            width={"100%"}
                            text='Create Request to all'
                            buttonImage='/svg/CreateRequestWhite.svg'
                            onClick={() => {
                                if (maintenanceStatus?.request) {
                                    toastNotification({
                                        type: constantOptions.TOAST_TYPE.ERROR,
                                        message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                                    })
                                } else {
                                    handleCreateRequest("ALL_MY_TG_GROUP_MEMBERS")
                                }
                            }}
                        />
                    }

                </div>
            </div>
        </div>
    )
}

const CustomLoader = () => <div className="ml-m-7px"><PulseSkeleton times={3} /></div>

