import React, { useState, useContext, useRef, useEffect } from 'react'
import NameInitials from '../../common/NameInitials'
import ENDPOINTS from "../../../constants/endpoints.json"
import toastNotification from '../../../utils/notifications/toastNotification'
import { UserContext } from '../../../pages/_app'
import constantOptions from '../../../constants/constantOptions'
import { useRouter } from 'next/router'
import { ThreeDots } from 'react-loader-spinner'
import { ModalContext } from '../../../context/ModalContext'
import { DrawerContext } from '../../../context/DrawerContext'
import RequestIcon from '../../icons/RequestIcon'
import { CustomStreamContext } from '../../../context/CustomStreamContext'
import SendMessageIcon from '../../icons/SendMessageIcon'
import canUserCreate1to1Request from '../../../utils/requestsV4/canUserCreate1to1Request'
import useThumbnail from '../../../hooks/useThumbnail'


export const MapAllMyFriendsListItem = ({
    friend,
    totalTgGroupMembers,
    playedFriends = false,
    requestType,
    handleCreateRequest,
    isSelfclub = false,
    canCreateRequest,
    source,
    // setOtherUserId=()=>{},
    setCreatorMode = () => { },
    setPegboardId = () => { },
    setSelectedPegboard = () => { },
    setNewClub = () => { },
    isGenderCompatible = true,
    customBackClickHandler,
}) => {
    const { name, profilePhoto, username } = friend?.friend_info || {}
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const { modal, setModal } = useContext(ModalContext)
    const { drawer, setDrawer } = useContext(DrawerContext)
    const router = useRouter()
    const { setOtherUserId } = useContext(CustomStreamContext)
    const { thumbnailUrl } = useThumbnail(profilePhoto, 128)

    const allClubs = friend?.groupNames || friend?.friend_info?.clubs || []

    const containerRef = useRef(null);
    const customButtonRef = useRef(null);
    const linkRef = useRef(null);


    useEffect(() => {
        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, []);


    const handleClickOutside = (event) => {
        if (containerRef.current && containerRef.current.contains(event.target)
            && customButtonRef.current && !customButtonRef.current.contains(event.target)
            && (linkRef.current ? !linkRef.current.contains(event.target) : true)
        ) {
            redirectToProfile();
        }
    };

    const sendMessage = async () => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS?.CREATE_ONE_TO_ONE_CHANNEL_STREAM, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    otherUserId: friend?.friend_id,
                }),
            })
                .then((data) => data.json())
                .then(async (data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        if (data?.channel?.id) {
                            router.push({
                                pathname: `/dashboard/my-tg`,
                                search: `?type=chat&channel_id=${data?.channel?.id}`,
                            });
                        }
                    }
                })
                .catch((error) => {
                    console.log(error)
                })
            setLoading(false)
        } catch (error) {
            console.log('Send message--->', error)
            setLoading(false)
        }
    }

    const redirectToProfile = () => {
        setOtherUserId(friend?.friend_id)
        if (source === 'pegboard-club') {
            setDrawer({
                type: "profile",
                global: drawer?.global,
                selectedPegboard: drawer?.selectedPegboard,
                customBackClickHandler: () => {
                    setDrawer({
                        global: drawer?.global,
                        type: "friends-played",
                        club: drawer?.club,
                        selectedPegboard: drawer?.selectedPegboard,
                        setCreatorMode: drawer?.setCreatorMode,
                        setPegboardId: drawer?.setPegboardId,
                        setSelectedPegboard: drawer?.setSelectedPegboard,
                        setNewClub: drawer?.setNewClub
                    })
                }
            })
        } else if (drawer?.source === "map-info-drawer") {
            setDrawer({
                type: "profile",
                source: "map",
                customBackClickHandler: customBackClickHandler,
                club: { type: drawer?.type, club: drawer?.club, clubColor: drawer?.clubColor }
            })
        } else {
            setDrawer({
                type: "profile",
                source: "map",
                customBackClickHandler: () => {
                    setDrawer()
                },
                club: { type: drawer?.type, club: drawer?.club, clubColor: drawer?.clubColor }
            })
        }
    }

    return (
        <div ref={containerRef} id='card' className='flex justify-between w-full py-sm border-b border-lightestgray px-md cursor-pointer hover:bg-lightestgray'
            onClick={(e) => {
                if (e.target.id === "card") {
                    redirectToProfile()
                }
            }}
        >
            <div className='flex items-center' id='card'>
                <div className="" id='card'>
                    {profilePhoto ? (
                        <img id='card'
                            src={thumbnailUrl}
                            className="mr-md rounded-full"
                            style={{
                                height: 45,
                                width: 45,
                                objectFit: 'cover',
                            }}
                            alt="Profile"
                        />
                    ) : (
                        <div className="mr-md" id='card'>
                            <NameInitials
                                rounded={"full"}
                                id={'card'}
                                contact={{ first_name: friend?.friend_id ? name : username }}
                                height={45}
                                width={45}
                                fontSize={20}
                            />
                        </div>
                    )}
                </div>
                <div>
                    {!totalTgGroupMembers ? (
                        <>
                            <div className='flex-col' id='card'>
                                <div className='flex' id='card'>
                                    <div id='card' className='text-16 font-medium'>{name}</div>
                                    {friend?.is_muted && <img id='card' className='ml-sm' src='/svg/MuteIcon.svg' alt="Muted" />}
                                </div>
                                <div className='flex'>
                                    {allClubs && allClubs.length > 0 ? (
                                        <>
                                            <div className='text-gray flex font-normal pt-xs' id='card'>
                                                <img width={13} className='mr-sm' src='/svg/location-grey.svg' alt="Location" />
                                                {allClubs[0]}
                                            </div>
                                            {allClubs?.length > 1 ? (
                                                <span id='card' ref={linkRef} className={`ml-xs pt-xs ${source === 'pegboard-club' ? 'text-darkteal font-normal' : 'text-black hover:underline font-medium cursor-pointer'}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation()
                                                        if (source === 'pegboard-club') {
                                                            null
                                                        } else {
                                                            setModal({
                                                                type: 'my-tg-common-groups',
                                                                groupNames: allClubs,
                                                            })
                                                        }
                                                    }}
                                                >
                                                    +{allClubs?.length - 1} Other{(allClubs?.length - 1) > 1 && "s"}
                                                </span>
                                            ) : null}
                                        </>
                                    ) : null}
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className='flex-col' id='card'>
                            <div className='flex'>
                                <div className='text-16 font-medium'>{!friend?.isFriend ? username : name}</div>
                                {friend?.isFriend ? (
                                    <div className='ml-md bg-tealTierBg text-10 font-ubuntu font-bold text-darkteal flex justify-evenly items-center rounded-lg' style={{ width: 82, height: 22 }}> <img width={13} src='/svg/FriendDarkTeal.svg' alt="Friend" />FRIENDS</div>
                                ) : null}
                                {friend?.isMuted &&
                                    <img className='ml-sm' src='/svg/MuteIcon.svg' alt="Muted" />
                                }
                            </div>
                            {allClubs && allClubs?.length > 0 && (
                                <div className='text-gray flex font-normal pt-xs' id='card'>
                                    <img width={15} className='mr-sm' src='/svg/Groupmember.svg' alt="Group" />
                                    {allClubs[0]}
                                    {allClubs?.length > 1 ? (
                                        <span className='ml-xs text-black font-medium cursor-pointer hover:underline' ref={linkRef}
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                setModal({
                                                    type: 'my-tg-common-groups',
                                                    groupNames: allClubs,
                                                    requestType: requestType
                                                })
                                            }}
                                        >
                                            +{allClubs?.length - 1} Other{(allClubs?.length - 1) > 1 && "s"}
                                        </span>
                                    ) : null}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
            <div className='flex items-center' ref={customButtonRef}>
                {(canUserCreate1to1Request(friend?.is_muted, isSelfclub, playedFriends, isGenderCompatible, ["FRIEND"].includes(requestType), canCreateRequest)) &&
                    <div className='cursor-pointer'
                        onClick={() => {
                            handleCreateRequest(requestType, friend?.friend_id)
                        }}
                        style={{ height: 30, width: 30 }}
                    >
                        <RequestIcon />
                    </div>
                }
                {loading ? (
                    <div className='ml-sm'>
                        <ThreeDots
                            visible={true}
                            height="25"
                            width="25"
                            color={"#098089"}
                            radius="9"
                            ariaLabel="three-dots-loading"
                            wrapperStyle={{}}
                            wrapperClass=""
                        />
                    </div>
                ) : (
                    <div className='px-sm cursor-pointer' onClick={() => {
                        if (drawer?.type === 'friends-played') {
                            // window?.localStorage.setItem("SELECTED_PEGBOARD", JSON.stringify({ selectedPegboard: drawer?.selectedPegboard }));
                        } else {
                            window?.sessionStorage.setItem("MAP_LIST_TYPE", JSON.stringify({ type: drawer?.type, club: drawer?.club, clubColor: drawer?.clubColor }));
                            window?.sessionStorage.setItem("MAP_CAN_CREATE_REQUEST", canCreateRequest);
                        }
                        sendMessage();
                        if (drawer?.type === "friends-played") {
                            setCreatorMode(false);
                            setPegboardId();
                            setSelectedPegboard();
                            setNewClub();
                        }
                    }}>
                        <SendMessageIcon />
                    </div>
                )}
            </div>
        </div>
    )
}
