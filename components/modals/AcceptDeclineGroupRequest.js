import React, { useContext, useState } from 'react'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from "../../constants/endpoints.json"
import { UserContext } from "../../pages/_app"
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'
import { TextArea } from '../common'
import Counter from '../common/Counter'

const AcceptDeclineGroupRequest = ({ modal, setModal }) => {

    const { user, token } = useContext(UserContext)
    const [loading, setLoading] = useState(false)
    const [reason, setReason] = useState("")
    const { action, request, setRefresh } = modal
    const { isMobile } = useCheckDeviceScreen()

    const closeModal = () => {
        setModal()
    }

    const handleGroupRequest = async () => {
        let URL = ENDPOINTS?.DECLINE_GROUP_REQUEST
        let body = {
            userId: user?.id,
            requestId: request?.id,
        }
        if (reason) {
            body.reason = reason
        }
        try {
            setLoading(true)
            await fetch(URL, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify(body)
            })
                .then((response) => response.json())
                .then((data) => {
                    if (!data?.status) {
                        setLoading(false)
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                        setModal()
                        setRefresh((prev) => prev + 1)
                    }
                })
        } catch (error) {
            console.log("Accept Group Request----->", error);
        }
    }

    return (
        <div className=''>
            <div className='text-center py-md'>Are you sure you want to decline this request?</div>
            <div className='flex flex-col'>

                <TextArea
                    title={"Add Reason"}
                    value={reason}
                    update={setReason}
                    rows={3}
                    placeholder="Enter here..."
                    maxLength={300}
                />
                <div className='flex justify-end'>
                    <Counter
                        count={reason?.length}
                        limit={300}
                        textSize='text-10'
                        color='text-grayLight'
                        extraClasses='text-right'
                    />
                </div>

                <div className='flex gap-md pt-md'>
                    <CustomButton
                        width={!isMobile ? 164 : 102}
                        height={45}
                        text='Cancel'
                        color='lightestgray'
                        onClick={closeModal}
                        textColor='black'
                        loading={loading}
                    />

                    <CustomButton
                        width={!isMobile ? 164 : 102}
                        height={45}
                        text={action === "accept" ? 'Accept' : 'Decline'}
                        color={action === "accept" ? 'darkteal' : 'red'}
                        loading={loading}
                        onClick={handleGroupRequest}
                    />
                </div>

            </div>
        </div>
    )
}

export default AcceptDeclineGroupRequest