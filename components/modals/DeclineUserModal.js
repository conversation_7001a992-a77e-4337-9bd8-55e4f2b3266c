import React, { useState, useEffect } from 'react';
import Select from '../common/Select';
import { UPDATE_USER } from '../../graphql/mutations/user';
import useClient from '../../graphql/useClient';
import moment from 'moment';
import ENDPOINTS from '../../constants/endpoints.json';
import TextArea from '../common/TextArea';
import Counter from '../common/Counter';
import { sendMailTemplate } from '../../utils/mailchimp';

export default function DeclineUserModal({ modal, setModal }) {
  const [declineReason, setDeclineReason] = useState();
  const [otherReason, setOtherReason] = useState();
  const [errors, setErrors] = useState({});
  const [showErrors, setShowErrors] = useState(false);
  const [loading, setLoading] = useState(false);
  const client = useClient();

  function validate() {
    if (!declineReason) {
      setErrors({
        declineReason: 'Please enter the reasoning for declining this user',
      });
      return false;
    } else if (declineReason === 'other' && !otherReason) {
      setErrors({
        otherReason: 'Please enter an explanation for declining this user',
      });
      return false;
    } else {
      setErrors({});
      return true;
    }
  }

  function declineUser() {
    setShowErrors(true);
    if (validate()) {
      const apiPath = window.location.pathname.includes('/admin/users')
        ? `../../${ENDPOINTS.HANDLE_DECLINED_USER_CLUBS}`
        : `../../../../${ENDPOINTS.HANDLE_DECLINED_USER_CLUBS}`;
      client
        .request(UPDATE_USER, {
          user_id: modal.user.id,
          user: {
            declined: declineReason === 'other' ? otherReason : declineReason,
            activate_later: false,
            account_activated: false,
            deactivated: false,
            declination_date: moment().format('YYYY-MM-DD'),
            activate_later_date: null,
          },
        })
        .then(async () => {
          fetch(apiPath, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: modal.user.id,
            }),
          })
            .then((data) => {})
            .catch((error) => console.log('error', error));

          await sendMailTemplate({
            email: modal.user.email,
            template_name: 'Deactivation Email 2',
            template_content: [
              {
                name: 'first_name',
                content: modal.user.first_name,
              },
              {
                name: 'reason',
                content:
                  declineReason === 'other' ? otherReason : declineReason,
              },
            ],
            subject: `Thousand Greens - Your Registration Request Has Been Declined`,
          });

          modal.refetchUsers();
          setModal();
        });
    }
  }

  useEffect(() => {
    if (showErrors) {
      validate();
    }
  }, [otherReason, declineReason, showErrors]);

  return (
    <div className="flex flex-col items-center pt-md w-full">
      <div className="w-full flex flex-col" style={{ maxWidth: 300 }}>
        <Select
          value={declineReason}
          update={setDeclineReason}
          options={['invalid club', 'duplicate', 'other']}
          placeholder="Select Reasoning For Declining User"
          disableError={!errors.declineReason}
          error={errors.declineReason}
        />
        {declineReason === 'other' && (
          <div className="mt-lg">
            <TextArea
              title="Add Reason"
              value={otherReason}
              update={setOtherReason}
              disableError={!errors.otherReason}
              error={errors.otherReason}
              placeholder="Enter here..."
              maxLength={300}
            />
            <div className="text-xs text-grayLight flex justify-end">
              <Counter count={otherReason?.length} limit={300} />
            </div>
          </div>
        )}
      </div>
      <div className="flex justify-between w-full mt-xl px-xl">
        <div
          onClick={() => setModal()}
          className="p-md flex-center uppercase text-sm font-regular text-gray hover:underline mb-sm cursor-pointer"
          style={{
            width: 150,
            height: 53,
            borderRadius: 10,
          }}
        >
          Cancel
        </div>
        <div
          onClick={() => declineUser()}
          className="p-md flex-center uppercase text-sm font-regular text-white bg-red hover:underline mb-sm cursor-pointer"
          style={{
            width: 220,
            height: 53,
            borderRadius: 10,
          }}
        >
          Decline User
        </div>
      </div>
    </div>
  );
}
