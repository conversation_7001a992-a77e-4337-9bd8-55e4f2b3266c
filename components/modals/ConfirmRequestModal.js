import React, { useState, useContext, useEffect } from 'react'
import { DateSelect, TextArea, TextInput } from '../common'
import GreenSubmitButton from '../buttons/GreenSubmitButton'
import useClient from '../../graphql/useClient'
import { CREATE_ONE_GAME, UPDATE_GAME } from '../../graphql/mutations/game'
import { UserContext } from '../../pages/_app'
import Link from 'next/link'
import { ACCEPT_REQUEST } from '../../graphql/mutations/request'
import Moment from 'moment'
import calculateGuestTimeRestrictionDates from '../../utils/clubs/calculateGuestTimeRestrictionDates'
import constantOptions from '../../constants/constantOptions'
import ENDPOINTS from '../../constants/endpoints.json'
import CustomButton from '../buttons/CustomButton'
import toastNotification from '../../utils/notifications/toastNotification'
import Counter from '../common/Counter'
import RequestBlockedIcon from '../icons/RequestBlockedIcon'
import adminClient from '../../graphql/adminClient'

const { DECLINE_REASON_MAX_LENGTH } = constantOptions

const GET_CLUB_DATA = `
query getClubData($club_id: Int!) {
    courses_by_pk(id: $club_id) {
        guest_time_restrictions
        closurePeriods
    }
}
`
function ConfirmLogistics({ request, confirm, cancel }) {
    return (
        <>
            <div className="text-24 py-md">Accept Request Confirmation</div>
            <div className="text-center text-grayLight px-xl">
                Have you confirmed the logistics for{' '}
                <div className="inline-block text-darkteal">#{request.game_id}</div>{' '}
                with{' '}
                <div className="inline-block text-darkteal">
                    {request.requestor_full_name}
                </div>
                ?
            </div>
            <div className="flex pt-xl">
                <CustomButton textColor='black' width={198} height={45} color="lightestgray" text="No" onClick={cancel} />
                <CustomButton width={198} height={45} color="darkteal" text="Yes" onClick={confirm} />
            </div>
        </>
    )
}

function SelectDate({ request, modal, setModal, globalDropdownState }) {
    const { refresh = () => { }, fetchUnreadMessageStatus = () => { }, fetchTabsCount = () => { } } = modal
    const client = useClient()
    const [date, setDate] = useState()
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const [error, setError] = useState()
    const [disabledDates, setDisabledDates] = useState([]);

    useEffect(() => {
        if (client && request) {
            getClubInfo();
        }
    }, [client, request])

    const editGame = async () => {
        if (date) {
            try {
                setLoading(true)
                await fetch(ENDPOINTS?.EDIT_GAME, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: request?.request_id,
                        gameDate: Moment(date).format('YYYY-MM-DD')
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            if (modal?.refresh) {
                                await refresh()
                            }
                            setLoading(false)
                            setModal()
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'This request has now been moved to your Accepted tab.',
                            })
                        }
                    })
            } catch (error) {
                console.log("Edit Game ------>", error);
            }
        } else {
            setError('Date is Required')
        }

    }

    const createGame = async () => {
        if (date) {
            try {
                setLoading(true)
                await fetch(ENDPOINTS?.ACCEPT_REQUEST, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: request?.request_id,
                        gameDate: Moment(date).format('YYYY-MM-DD')
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            await fetchUnreadMessageStatus()
                            await fetchTabsCount()
                            await refresh()
                            setModal()
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'This request has now been moved to your Accepted tab.',
                            })

                        }
                    })
            } catch (error) {
                console.log("Accept Request------", error);
            }
        } else {
            setError('Date is required')
        }
    }

    async function getClubInfo() {
        const {
            courses_by_pk: {
                guest_time_restrictions: guestTimeRestrictions,
                closurePeriods
            }
        } = await client.request(GET_CLUB_DATA, {
            club_id: request.club_id
        })

        let allDisabledDates = [];
        if (closurePeriods && closurePeriods.length > 0) {
            const closurePeriodDates = closurePeriods
                .map(({ from, to }) => {
                    const startDate = Moment(from).add(-1, 'days');
                    const endDate = Moment(to);
                    const nDaysBetween = endDate.diff(startDate, 'd')
                    const dates = new Array(nDaysBetween)
                        .fill('')
                        .map((_, index) => {
                            return Moment(from).add(index, 'days').toDate()
                        })
                    return dates
                })
                .flat()
            allDisabledDates = [...closurePeriodDates];
        }

        // guestTimeRestrictions is actually guest time availability - the verbiage for it has been changed in recent past (23 Aug 2021)
        // On 18th Jan 2023, we have applied MAX_MONTHS_ALLOWED_FOR_REQUEST_ACCEPTANCE as we have to have a certain end date for showing guestTimeRestrictions in calendar
        if (guestTimeRestrictions && typeof guestTimeRestrictions === 'object') {
            let guestTimeRestrictionDates = calculateGuestTimeRestrictionDates(guestTimeRestrictions, constantOptions?.MAX_YEARS_ALLOWED_FOR_REQUEST_ACCEPTANCE * 12);
            allDisabledDates = [...allDisabledDates, ...guestTimeRestrictionDates];
        }
        setDisabledDates(allDisabledDates)
    }

    return (
        <>
            <div className="text-24 py-md">{modal?.subType === 'edit' ? 'Edit Game Date' : 'Accept Request Confirmation'}</div>

            <div className="w-full px-lg">
                <div className="mb-sm text-grayLight">Please select the date you are available to play:</div>
                <DateSelect
                    value={date}
                    update={setDate}
                    minDate={Moment().toDate()}
                    singular
                    disableTitle
                    placeholder="Select Date"
                    title="Date"
                    calendarIcon='/svg/calendar.svg'
                    error={error}
                    disabledDates={disabledDates}
                    globalDropdownState={globalDropdownState}
                    maxDate={Moment().add(constantOptions?.MAX_YEARS_ALLOWED_FOR_REQUEST_ACCEPTANCE, 'years').format()}
                />
            </div>
            <div className="flex mt-md">
                <CustomButton
                    height={45}
                    width={198}
                    color='lightestgray'
                    text='Cancel'
                    textColor='black'
                    onClick={() => setModal()}
                />
                <CustomButton
                    loading={loading}
                    height={45}
                    width={198}
                    onClick={() => {
                        if (modal?.subType === 'edit') {
                            editGame()
                        } else {
                            createGame()
                        }
                    }}
                    textColor='white'
                    text='Confirm'
                />
            </div>
        </>
    )
}

function CancelConfirm({ setModal, request }) {
    return (
        <>
            <div className="text-24 py-md">Accept Request Confirmation</div>
            <div className="text-center text-grayLight px-xl">
                You must finalize the logistics before accepting the request. Chat with {' '}
                <div className="inline-block text-darkteal">
                    {request.requestor_full_name}
                </div> {' '}
                again or cancel anyways.
            </div>

            <div className="flex pt-xl">
                <CustomButton
                    height={45}
                    width={198}
                    color='lightestgray'
                    text='Cancel'
                    textColor='black'
                    onClick={() => setModal()}
                />
                <Link
                    href={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || request?.stream_channel_id ? "-chat" : ""}/[request_id]`}
                    as={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || request?.stream_channel_id ? "-chat" : ""}/${request.request_id}`}>
                    <div>
                        <CustomButton
                            text={`Chat Again`}
                            height={45}
                            width={198}
                            onClick={() => setModal()}
                        />
                    </div>
                </Link>
            </div>
        </>
    )
}

function DeclineRequest({ modal, setModal, request, requestType, declineReasonMandatory }) {
    const [reason, setReason] = useState()
    const [errors, setErrors] = useState({})
    const [loading, setLoading] = useState(false)
    const { user, token } = useContext(UserContext)
    const { fetchUnreadMessageStatus = () => { }, fetchTabsCount = () => { }, refresh = () => { } } = modal
    const [isWithinHours, setIsWithinHours] = useState(false)

    useEffect(() => {

        const displayMessage = async () => {
            const gameDate = Moment(modal.request.game_date);
            const now = Moment();
            const hoursDifference = gameDate.diff(now, 'hours');
            const thresholdTime = await adminClient.request(`
                {
                    system_setting(where: {name: {_eq: "last_minute_request_cancellation_threshold"}}) {
                        value
                    }
                }
            `)
            setIsWithinHours(hoursDifference <= Number(thresholdTime.system_setting[0]?.value?.value));
        }
        // Check if game date is within 48 hours
        if (modal?.request?.game_date) {
            displayMessage()
        }
    }, [modal?.request?.game_date]);

    function validate() {
        if (declineReasonMandatory && (!reason || (reason && reason.trim().length === 0))) {
            setErrors({
                reason:
                    'Please enter the reasoning for declining the request',
            })
            return false
        }
        return true;
    }

    const declineRequest = async () => {
        if (validate()) {
            try {
                setLoading(true)
                await fetch(ENDPOINTS?.DECLINE_REQUEST, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: modal?.request?.request_id,
                        deleteReason: reason,
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            await modal.fetchUnreadMessageStatus()
                            await modal.fetchTabsCount()
                            await refresh()
                            setModal()
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: 'This request has now been cancelled and moved to your history tab.',
                            })
                        }
                    })
            } catch (error) {
                console.log("Create Group Modal------", error);
            }

            await fetchUnreadMessageStatus()
            await fetchTabsCount()
            await refresh()
            setModal()
        }
    }

    return (
        <>
            <div className="text-24 py-md">Decline Request</div>

            <div className="text-center text-grayLight px-xl mb-md">
                Are you sure you would like to decline this request?
            </div>

            {isWithinHours ? <div className={`rounded-lg mx-lg flex items-start text-12 text-gray bg-lightRed py-sm px-md mb-sm`}>
                <RequestBlockedIcon isBlocked={true} /> Declining requests last minute/short notice are disruptive and we don't encourage.
            </div> : null}

            {["received/accepted", "received/open"].includes(requestType) ?
                <div className="w-full px-lg">
                    <TextArea
                        title={'Add Reason'}
                        value={reason}
                        update={(value) => {
                            setReason(value)
                            setErrors({})
                        }}
                        placeholder={modal?.decliningRequest ? "Enter Here.." : "Please elaborate reason for the dissatisfaction"}
                        error={errors.reason}
                        disableError={true}
                        maxLength={DECLINE_REASON_MAX_LENGTH}

                    />

                    {(modal?.declineReasonMandatory && errors?.reason) && <div className='text-red text-center'>{errors?.reason}</div>}

                    <div className='flex justify-end'>
                        <Counter textSize='text-10' color='text-grayLight' count={reason?.length} limit={DECLINE_REASON_MAX_LENGTH} />
                    </div>
                </div>
                : null
            }
            <div className="flex mt-md">
                <CustomButton
                    height={45}
                    width={198}
                    color='lightestgray'
                    text='Cancel'
                    textColor='black'
                    onClick={() => setModal()}
                />
                <CustomButton
                    loading={loading}
                    height={45}
                    width={198}
                    onClick={() => declineRequest()}
                    color='red'
                    textColor='white'
                    text='Decline Request'
                />
            </div>
        </>
    )
}

export default function ConfirmRequestModal({
    modal,
    setModal,
    globalDropdownState,
}) {
    const [activeTab, setActiveTab] = useState(
        modal?.subType === 'decline'
            ? 'decline-request'
            : modal?.subType === 'edit'
                ? 'select-date' : 'confirm-logistics'
    )

    return (
        <div className="flex flex-col items-center w-full">
            {activeTab === 'confirm-logistics' ? (
                <ConfirmLogistics
                    request={modal.request}
                    confirm={() => setActiveTab('select-date')}
                    cancel={() => setActiveTab('cancel-confirm')}
                />
            ) : activeTab === 'select-date' ? (
                <SelectDate
                    modal={modal}
                    request={modal.request}
                    setModal={setModal}
                    refresh={modal.refresh}
                    globalDropdownState={globalDropdownState}
                />
            ) : activeTab === 'decline-request' ? (
                <DeclineRequest
                    modal={modal}
                    setModal={setModal}
                    request={modal.request}
                    refresh={modal.refresh}
                    requestType={modal.requestType}
                    declineReasonMandatory={modal?.declineReasonMandatory}
                />
            ) : (
                <CancelConfirm setModal={setModal} request={modal.request} />
            )}
        </div>
    )
}
