import React from 'react'
import NameInitials from '../common/NameInitials'
import dateFormatter from '../../utils/helper/dateFormatter'

export default function GameRequesterInfoModal({ modal, setModal }) {
  const { request } = modal
  return (

    <div
      className="bg-white px-md rounded-lg flex flex-col w-full h-full">

      <div className="text-21 font-medium flex mb-xs pb-sm justify-between">
        <div>
        Request
        #{request?.game_id}
        </div>
        <div>
          <img onClick={() => setModal()} src="/svg/CrossIconBlack.svg" />
        </div>
      </div>

      <div className="text-gray text-12 flex">
        <div>
          <img className='mr-sm' src="/svg/GolfPostDarkTeal.svg" />
        </div>
        <div className='flex flex-col'>
          <div className=''>Club Name</div>
          <div className="mb-md text-16 font-medium text-black">{request?.club?.name}</div>
        </div>
      </div>
      <div className="text-gray text-12 flex">
        <div>
          <img className='mr-sm' src="/svg/Calendar_New.svg" />
        </div>
        <div className='flex flex-col'>
          <div className=''>Requested Dates</div>
          <div className="mb-md text-16 text-black">{dateFormatter(request?.start_date, request?.end_date)}</div>
        </div>
      </div>
      <div className="text-gray text-12 flex">
        <div>
          <img className='mr-sm' src="/svg/Players.svg" />
        </div>
        <div className='flex flex-col'>
          <div className=''># of Players</div>
          <div className="mb-md text-16 text-black">
            {request?.number_of_players}
          </div>
        </div>
      </div>

      <div className="text-12 mb-md break-word">{request?.message}</div>


      {request?.criteria?.accompanied_only
        ? <div className="">
          Accompanied Only
        </div>
        : null}
    </div>
  )
}