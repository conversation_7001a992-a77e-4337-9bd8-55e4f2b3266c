import React, { useState, useContext, useEffect } from 'react'
import { UserContext } from '../../pages/_app'
import Select from '../chat-v2/Select'
import CustomButton from '../buttons/CustomButton'
import ENDPOINTS from '../../constants/endpoints.json'
import { DateSelect } from '../common'
import XIcon from '../icons/RequestV2/XIcon'
import Moment from 'moment'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'

const AdminCompleteRequestModal = ({ modal, setModal }) => {
    const { isCompletable, request, setRefreshGames } = modal
    const [userSearchValue, setUserSearchValue] = useState('')
    const [selectedHost, setSelectedHost] = useState()
    const [gameDate, setGameDate] = useState()
    const [globalDropdownVisible, setGlobalDropdownVisible] = useState(false)
    const { user, token } = useContext(UserContext)
    const [loading, setLoading] = useState(false)

    const completeGame = async () => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS?.ADMIN_COMPLETE_GAME, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    hostId: selectedHost?.id,
                    gameDate: Moment(gameDate).format('YYYY-MM-DD'),
                    requestId: request?.id,
                    gameId: request?.game_id
                })
            })
                .then((response) => response.json())
                .then((data) => {
                    if (!data?.status) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: data?.message,
                        })
                    } else {
                    }
                })
            setLoading(false)
            setRefreshGames((prev) => prev + 1)
            setModal()
        } catch (error) {
            console.log("Create Group Modal------", error);
        }

    }


    return (
        <div className='w-full px-32 items-stretch'>
            {(isCompletable) &&
                <div>
                    <div className='flex bg-lightestgray rounded-lg px-sm mt-md' style={{ zIndex: 1000 }}>
                        <div className='mr-sm pt-[10px]'>
                            <img width={15} src='/svg/search.svg'></img>
                        </div>
                        <Select
                            placeholder={"Search First, Last, full or username"}
                            value={userSearchValue}
                            updateSearch={setUserSearchValue}
                            alreadyRegistered={true}
                            update={(value) => {
                                setSelectedHost(value)
                            }}
                            disableError={true}
                            type="admin-search-hosts"
                            hostsSent={request?.hosts_sent}
                            requestId={request?.id}
                            user={user}
                            groupId={modal?.groupId}
                            token={token}
                            globalDropdownState={{
                                globalDropdownVisible,
                                setGlobalDropdownVisible,
                            }}
                            className={'text-12 border-none bg-lightestgray '}
                        />
                    </div>
                    {selectedHost && <div className='bg-lightestgray rounded-lg p-md mt-md'>
                        <div className='flex w-full justify-between'>
                            <div>{selectedHost?.first_name} {selectedHost?.last_name}</div>
                            <div className='cursor-pointer' onClick={() => setSelectedHost()}><XIcon color={'#CCCCCC'} /></div>
                        </div>
                        <div className='flex py-xs'>
                            <div className='text-gray font-thin' style={{ minWidth: 100 }}>Username</div>
                            <div>{selectedHost?.username}</div>
                        </div>
                        <div className='flex'>
                            <div className='text-gray font-thin' style={{ minWidth: 100 }}>Email</div>
                            <div>{selectedHost?.email}</div>
                        </div>
                    </div>}
                    <div className='mt-xl'>
                        <DateSelect
                            title={'Select the game date'}
                            placeholder={'Select the dates'}
                            zIndex={1}
                            value={gameDate}
                            minDate={Moment().subtract(1, 'year').calendar()}
                            update={(value) =>
                                setGameDate(value)
                            }
                        />
                    </div>
                </div>
            }
            <div className='flex justify-evenly self-end mt-xl'>
                <CustomButton
                    text='Dismiss'
                    color='lightestgray'
                    textColor=''
                    onClick={() => setModal()}
                    disabled={loading}
                />
                <CustomButton
                    text='Complete Game'
                    onClick={completeGame}
                    disabled={!selectedHost || !gameDate}
                    loading={loading}
                />
            </div>
        </div>
    )
}

export default AdminCompleteRequestModal