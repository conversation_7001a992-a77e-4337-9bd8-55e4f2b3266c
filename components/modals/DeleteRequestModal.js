import React, { useState, useContext, useEffect } from 'react'
import { TextArea, TextInput } from '../common'
import constantOptions from '../../constants/constantOptions'
import CustomButton from '../buttons/CustomButton'
import toastNotification from '../../utils/notifications/toastNotification'
import ENDPOINTS from '../../constants/endpoints.json'
import { UserContext } from '../../pages/_app'
import { UPDATE_REQUEST } from '../../graphql/mutations/request'
import useClient from '../../graphql/useClient'
import moment from 'moment'
import adminClient from '../../graphql/adminClient'
import RequestBlockedIcon from '../icons/RequestBlockedIcon'

export default function DeleteRequestModal({ modal, setModal }) {
    const client = useClient()
    const [deletedReason, setDeletedReason] = useState();
    const [errors, setErrors] = useState({})
    const { user, token } = useContext(UserContext)
    const [loading, setLoading] = useState(false)
    const [isWithinHours, setIsWithinHours] = useState(false)

    useEffect(() => {
        const displayMessage = async () => {
            const gameDate = moment(modal.request.game_date);
            const now = moment();
            const hoursDifference = gameDate.diff(now, 'hours');
            const thresholdTime = await adminClient.request(`
                {
                    system_setting(where: {name: {_eq: "last_minute_request_cancellation_threshold"}}) {
                        value
                    }
                }
            `)
            setIsWithinHours(hoursDifference <= Number(thresholdTime.system_setting[0]?.value?.value));
        }
        // Check if game date is within 48 hours
        if (modal?.request?.game_date) {
            displayMessage()
        }
    }, [modal?.request?.game_date]);

    function validate() {
        if (modal?.declineReasonMandatory && (!deletedReason || (deletedReason && deletedReason.trim().length === 0))) {
            setErrors({
                reason: 'Please enter the reasoning for deleting the request',
            })
            return false
        }
        return true;
    }

    const handleRequest = async () => {
        let URL = (modal?.request?.requestor_user_id === user?.id) ? ENDPOINTS?.CANCEL_REQUEST : ENDPOINTS?.DECLINE_REQUEST
        if (validate()) {
            try {
                setLoading(true)
                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: modal?.request?.request_id,
                        deleteReason: deletedReason,
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            await modal?.fetchTabsCount()
                            await modal?.fetchUnreadMessageStatus()
                            setModal()
                            await modal.fetchRequests((prev) => prev + 1)
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.REQUEST_MOVED,
                                message: modal?.requestType === 'requested/accepted' ? 'This request has now been cancelled and moved to your history tab.' : 'This request has now been moved to your history.',
                            })
                        }
                    })
            } catch (error) {
                console.log("Create Group Modal------", error);
            }
        }
    }

    const deleteRequest = async () => {
        let URL = ENDPOINTS?.DELETE_GAME
        if (validate()) {
            try {
                setLoading(true)
                await fetch(URL, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        ['Authorization']: `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                        userId: user?.id,
                        requestId: modal?.request?.request_id,
                    })
                })
                    .then((response) => response.json())
                    .then(async (data) => {
                        if (!data?.status) {
                            toastNotification({
                                type: constantOptions.TOAST_TYPE.ERROR,
                                message: data?.message,
                            })
                        } else {
                            await modal?.fetchTabsCount()
                            await modal?.fetchUnreadMessageStatus()
                            setModal()
                            await modal.fetchRequests((prev) => prev + 1)
                        }
                    })
            } catch (error) {
                console.log("Create Group Modal------", error);
            }
        }
    }

    const deleteRequestByAdmin = async () => {
        let request = {
            status: 'cancelled',
            cancelled_by: constantOptions?.REQUEST_CANCELLED_BY?.REQUESTER
        };
        await client.request(UPDATE_REQUEST, {
            id: modal?.request?.request_id,
            request
        })
        await modal.fetchRequests((prev) => prev + 1)
        setModal()

    }

    return (
        <div>
            <>
                <div className="text-24 font-medium py-md text-center">{`${modal?.requestType === 'history' ? "Remove" : "Decline"} Request`}</div>
                <div className="text-center text-grayLight px-xl mb-md">
                    {modal?.requestType === 'history' ? `Are you sure you would like to remove this request from your history?` : `Are you sure you would like to Decline this request?`}
                </div>
                {isWithinHours ? <div className={`rounded-lg mx-lg flex items-start text-12 text-gray bg-lightRed py-sm px-md mb-sm`}>
                    <RequestBlockedIcon isBlocked={true} /> Declining requests last minute/short notice are disruptive and we don't encourage.
                </div> : null}
                {["requested/open", "requested/accepted"].includes(modal?.requestType) ?
                    <div className="w-full px-lg">
                        <TextArea
                            title={'Add Reason'}
                            value={deletedReason}
                            update={(value) => {
                                setErrors({})
                                setDeletedReason(value)
                            }}
                            placeholder={modal?.decliningRequest ? "Enter Here .." : "Please elaborate reason for the dissatisfaction"}
                            error={errors.reason}
                            disableError={true}

                        />
                    </div>
                    : null
                }
                {(modal?.declineReasonMandatory && errors?.reason) && <div className='text-red text-center'>{errors?.reason}</div>}
                <div className="flex mt-md justify-evenly">
                    <CustomButton onClick={() => setModal()} text='Cancel' height={45} width={198} borderRadius={10} color='lightestgray' textColor='gray' loading={loading} />
                    <CustomButton onClick={() => {
                        if (user?.role === 'admin') {
                            deleteRequestByAdmin()
                        } else {
                            if (["requested/open", "requested/accepted"].includes(modal.requestType)) {
                                handleRequest()
                            } else {
                                deleteRequest()
                            }
                        }
                    }} text={modal?.requestType === 'history' ? 'Remove Request' : 'Decline Request'} height={45} width={198} borderRadius={10} color='red' textColor='white' loading={loading} />
                </div>
            </>
        </div>

    )
}
