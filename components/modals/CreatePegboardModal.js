import React, { useState, useEffect, useContext } from 'react'
import { UserContext } from '../../pages/_app'
import { Select, TextInput } from '../common'
import Checkbox2 from '../common/Checkbox2'
import Checkbox from '../chat-v2/CheckBox'
import CustomButton from '../buttons/CustomButton'
import InfoIcon from '../icons/InfoIcon'
import CheckboxMultiselect from '../common/CheckboxMultiselect'
import ENDPOINTS from '../../constants/endpoints.json'
import toastNotification from '../../utils/notifications/toastNotification'
import constantOptions from '../../constants/constantOptions'
import { useRouter } from 'next/router'
import Loader from "react-loader-spinner"

const CreatePegboardModal = ({ modal, setModal }) => {
  const { user, token } = useContext(UserContext)
  const [title, setTitle] = useState('')
  const [isDuplicate, setIsDuplicate] = useState(false)
  const [allTG, setAllTg] = useState(true)
  const [allMyFriends, setAllMyFriends] = useState(false)
  const [onlyTgGroups, setOnlyTgGroups] = useState(false)
  const [myTgCommunity, setMyTgCommunity] = useState(false)
  const [errors, setErrors] = useState({})
  const [duplicateName, setDuplicateName] = useState('')
  const [allMyTgGroups, setAllMyTgGroups] = useState([])
  const [groups, setGroups] = useState([])
  const [allGroupsSelected, setAllGroupsSelected] = useState(true)
  const [loading, setLoading] = useState(false)
  const [totalGroupsCount, setTotalGroupsCount] = useState(0)
  const [page, setPage] = useState(1)
  const [privatePegboard, setPrivatePegboard] = useState(true)
  const [isDisabled, setIsDisabled] = useState(modal?.pegboard || true)
  const router = useRouter()

  const pageSize = process.env.CONFIG.MY_GROUP_LIST_LIMIT

  useEffect(() => {
    fetchGroupsList()
  }, [])

  useEffect(() => {
    if (modal?.pegboard) {

      setTitle(modal?.pegboard?.name)
      setAllTg(modal?.pegboard?.visible_to_all)
      setPrivatePegboard(modal?.pegboard?.isPrivate)

      if (modal?.pegboard?.forFriends || modal?.pegboard?.my_tg_group_id?.length) {
        if (modal?.pegboard?.forFriends) {
          setAllMyFriends(true)
          setMyTgCommunity(true)
        }
      }
      if (modal?.pegboard?.my_tg_group_id?.length) {
        setOnlyTgGroups(true)
        setMyTgCommunity(true)
        if (modal?.pegboard?.my_tg_group_id[0] === -1 && modal?.pegboard?.my_tg_group_id?.length === 1) {
          setAllGroupsSelected(true)
        } else {
          setAllGroupsSelected(false)
          // const temp = [...allMyTgGroups]
          setGroups(modal?.pegboard?.chatChannels)
          // const updated = allMyTgGroups?.filter((t) => modal?.pegboard?.my_tg_group_id.includes(t?.id))
          // setGroups(updated);
        }
      }

    }
  }, [modal?.pegboard, allMyTgGroups])

  const fetchGroupsList = async (pageToLoad = page) => {
    const URL = user?.role === 'admin' ? ENDPOINTS?.ADMIN_GET_GROUPS_FOR_PEGBOARDS : ENDPOINTS?.GET_GROUPS_FOR_PEGBOARD

    let body = {
      limit: pageSize,
      page: pageToLoad - 1
    }

    if (user?.role === 'user') {
      body = {
        ...body,
        userId: user?.id
      }
    }

    await fetch(URL, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify(body)
    })
      .then((response) => response.json())
      .then((data) => {
        if (pageToLoad !== 1) {
          setAllMyTgGroups((prev) => [...prev, ...data?.groups])
          setTotalGroupsCount(data?.totalCount)
        } else {
          setAllMyTgGroups(data?.groups)
          setTotalGroupsCount(data?.totalCount)
        }
      })
    setPage(pageToLoad)
  }

  const savePegboard = async () => {
    setLoading(true)
    const URL = user?.role === 'admin' ? ENDPOINTS?.ADMIN_EDIT_PEGBOARD : ENDPOINTS?.EDIT_PEGBOARD
    let body = {
      title,
      pegboardId: modal?.pegboard?.id,
      visibleToAllTG: allTG,
      myTGGroupId: (privatePegboard || !onlyTgGroups) ? [] : allGroupsSelected ? [-1] : groups?.map((g) => g.id)
    }
    if (user?.role === 'user') {
      body = {
        ...body,
        userId: user?.id,
        forFriends: allMyFriends,
        isPrivate: privatePegboard
      }
    }
    await fetch(URL, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify(body)
    })
      .then((response) => response.json())
      .then((data) => setAllMyTgGroups(data?.groups))
    setLoading(false)
    setModal()
    modal?.setRefresh((prev) => prev + 1)
  }

  const addPegboard = async () => {
    setLoading(true)
    const URL = user?.role === 'admin' ? ENDPOINTS?.ADMIN_ADD_PEGBOARD : ENDPOINTS?.CREATE_PEGBOARD
    let body = {
      title,
      visibleToAllTG: allTG,
      myTGGroupId: ((privatePegboard || !onlyTgGroups) && user?.role !== 'admin') ? [] : allGroupsSelected ? [-1] : groups.map((g) => g.id),
      copyPegboard: isDuplicate,
      existingPegboardId: duplicateName?.id
    }

    if (user?.role !== 'admin') {
      delete body.visibleToAllTG

      body = {
        ...body,
        userId: user?.id,
        forFriends: allMyFriends,
        isPrivate: privatePegboard,
      }
    }
    await fetch(URL, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        ['Authorization']: `Bearer ${token}`,
      },
      body: JSON.stringify(body)
    })
      .then((response) => response.json())
      .then((data) => {
        if (data?.status) {
          setAllMyTgGroups(data?.groups)
          toastNotification({
            type: constantOptions.TOAST_TYPE.PEGBOARD_CREATED,
            message: "Pegboard Created Successfully"
          })
        } else {
          toastNotification({
            type: constantOptions.TOAST_TYPE.ERROR,
            message: data?.message,
          })
        }
        if (user?.role === 'admin') {
          router.push({
            pathname: `/admin/pegboards/${data?.pegboard?.id}`
          })
        } else {
          modal?.setOpenNewPegboard(data?.pegboard)
          router.push({
            pathname: `/dashboard/play/`,
            search: `?type=pegboards&id=${data?.pegboard?.id}`,
          })
        }
      })
    setLoading(false)
    setModal()
    modal?.setRefresh((prev) => prev + 1)
  }


  const handleSelection = (group) => {
    if (modal?.pegboard) {
      setIsDisabled(false)
    }
    const temp = groups.map(group => group?.id)
    if (temp?.includes(group?.id)) {
      const updated = groups.filter((t) => t.id !== group?.id)
      if (updated?.length === 0) {
        setAllGroupsSelected(true)
      }
      setGroups(updated)

    } else {
      const updated = [...groups, group]
      if (updated?.length === allMyTgGroups?.length && groups?.length) {
        setAllGroupsSelected(true)
        setGroups([])
      } else {
        setAllGroupsSelected(false)
        setGroups(updated)
      }
    }
  }

  return (
    <div className={`w-full px-lg ${loading ? 'pointer-events-none' : ''}`}>
      <div className='flex flex-col md:flex-row gap-10 mt-xxl'>
        <div className='flex-1'>
          <TextInput
            addClubClasses={true}
            value={title}
            maxLength={100}
            update={(value) => {
              setTitle(value)
              if (value?.length < 3 && value?.trim() === '') {
                setIsDisabled(true)
              } else {
                setIsDisabled(false)
              }
            }}
            disableError={!errors?.title} title={"Pegboard Title"} placeholder={"Title Here"} />
          <div className='mt-md mb-sm'>
            {modal?.pegboard ? (
              <div className='flex text-grayLight'>
                <div className='mt-1 mr-1'><InfoIcon /></div>
                If you want to add/remove clubs in this pegboard, you can do the same by going into the Pegboard.
              </div>

            ) : (
              <Checkbox value={isDuplicate} onClick={() => setIsDuplicate(!isDuplicate)} label={"Do you want to duplicate existing Pegboard’s club?"} />
            )}
          </div>
          {isDuplicate &&
            <Select
              addClubClasses={true}
              titleWeight={'thin'}
              fontWeignt={'normal'}
              title={'Select Pegboard'}
              placeholder={'Select'}
              options={modal?.pegboards}
              value={duplicateName}
              update={(value) => {
                setDuplicateName(value)
              }}
            />}
        </div>

        <div className='flex-1'>
          <div className='text-14 text-grayLight'>Audience (Create pegboard for)</div>
          <div className={`py-md`}>
            {user?.role === 'admin' ? (
              <>
                <div className=''>
                  <Checkbox2
                    customClasses={'text-14 w-fit cursor-pointer'}
                    label="All Thousand Greens Users"
                    isDisabled={false}
                    value={allTG}
                    onClick={() => {
                      if (!onlyTgGroups && allTG) {
                        null
                      } else {
                        setOnlyTgGroups(false)
                        setAllTg(!allTG)
                        if (title?.length >= 3) {
                          setIsDisabled(false)
                        }
                      }
                    }}
                    roundedButton={true}
                  />
                </div>

                <div className='mt-sm mb-sm'>
                  <Checkbox2
                    customClasses={'text-14 w-fit cursor-pointer'}
                    label="TG Groups"
                    isDisabled={false}
                    value={onlyTgGroups}
                    onClick={() => {
                      if (onlyTgGroups && !allTG) {
                        null
                      } else {
                        setAllTg(false)
                        setOnlyTgGroups(!onlyTgGroups)
                        if (title?.length >= 3) {
                          setIsDisabled(false)
                        }
                      }
                    }}
                    roundedButton={true}
                  />
                </div>
              </>
            ) : (
              <>
                <div className='flex items-center mb-sm'>
                  <Checkbox2
                    roundedButton={true}
                    customClasses={'text-14 w-fit cursor-pointer'}
                    label="Private"
                    isDisabled={false}
                    value={privatePegboard}
                    onClick={() => {
                      setPrivatePegboard(true)
                      setMyTgCommunity(false)
                      setAllMyFriends(false)
                      setOnlyTgGroups(false)
                      if (groups?.length) {
                        setGroups([])
                        setAllGroupsSelected(true)
                      }
                      if (title?.length >= 3) {
                        setIsDisabled(false)
                      }
                    }}
                  />
                  <div className='text-12 ml-xs'>(Visible to me only)</div>
                </div>
                <Checkbox2
                  roundedButton={true}
                  customClasses={'text-14 w-fit cursor-pointer'}
                  label="My TG Community"
                  isDisabled={false}
                  value={myTgCommunity}
                  onClick={() => {
                    setMyTgCommunity(true)
                    setPrivatePegboard(false)
                    if (!myTgCommunity) {
                      setAllMyFriends(true)
                      setOnlyTgGroups(true)
                    }
                    if (title?.length >= 3) {
                      setIsDisabled(false)
                    }
                  }}
                />
                <div className={`pl-lg mt-sm ${myTgCommunity ? '' : 'pointer-events-none'}`}>
                  <Checkbox
                    label={"All My Friends"}
                    customClasses={`text-14 w-fit cursor-pointer ${myTgCommunity ? '' : 'text-gray'}`}
                    value={allMyFriends}
                    onClick={() => {
                      if (title?.length >= 3) {
                        setIsDisabled(false)
                      }
                      if (!allMyTgGroups?.length && allMyFriends) {
                        null
                      } else {
                        setAllMyFriends((prev) => !prev)
                      }
                      if (allMyFriends && !onlyTgGroups && allMyTgGroups?.length) {
                        setOnlyTgGroups(true)
                      } else {
                        null
                      }
                    }}
                    isDisabled={!myTgCommunity}
                  />
                  <Checkbox label={"My Groups"}
                    customClasses={`text-14 w-fit cursor-pointer ${myTgCommunity ? '' : 'text-gray'}`}
                    value={onlyTgGroups}
                    onClick={() => {
                      if (title?.length >= 3) {
                        setIsDisabled(false)
                      }
                        setOnlyTgGroups((prev) => !prev)
                      if (!allMyFriends && onlyTgGroups && allMyTgGroups?.length) {
                        setAllMyFriends(true)
                      }
                    }}
                    isDisabled={!myTgCommunity}
                  />
                </div>
              </>
            )}
            <>
              {(onlyTgGroups || user?.role === 'user') && allMyTgGroups?.length ?
                <div className={`${user?.role === 'user' ? 'ml-[50px]' : ''} ${(onlyTgGroups && myTgCommunity || user?.role === 'admin' && onlyTgGroups) ? '' : 'pointer-events-none'}`}>
                  <CheckboxMultiselect
                    title={'Select Group'}
                    placeholder={'Select'}
                    options={allMyTgGroups}
                    addClubClasses={true}
                    titleWeight={'normal'}
                    fontWeignt={'normal'}
                    update={handleSelection}
                    textSize='16'
                    groups={groups}
                    setGroups={setGroups}
                    value={groups}
                    allGroupsSelected={allGroupsSelected}
                    showAll={allGroupsSelected}
                    setPage={setPage}
                    totalGroupsCount={totalGroupsCount}
                    fetchGroupsList={fetchGroupsList}
                    page={page}
                    pageSize={pageSize}
                    setAllGroupsSelected={setAllGroupsSelected}
                    setIsDisabled={setIsDisabled}
                  />
                </div> :
                <>
                  {user?.role === 'user' &&
                    <div className='flex bg-tealTierBg rounded-lg text-12 text-gray ml-[28px] mt-sm p-sm'>
                      <div className='mr-1 mt-1'><InfoIcon color='#098089' /></div>
                      You're not in any TG Group. Selecting 'TG Groups' when creating the Pegboard will display it in future groups; otherwise, it won't.
                    </div>
                  }
                </>
              }
            </>
          </div>
        </div>
      </div>
      <div className={`flex flex-col md:flex-row w-full justify-${modal?.pegboard ? 'end' : 'between'}`}>
        {modal?.pegboard ? null :
          <div className='flex items-center'>
            <InfoIcon />
            <div className='pl-xs text-gray'>
              You can add clubs after creating pegboard
            </div>
          </div>
        }

        <div className='flex justify-evenly md:justify-end pt-md md:pt-0'>
          <CustomButton loading={loading} onClick={() => setModal()} color='lightestgray' textColor='black' text='Cancel' />
          <CustomButton loading={loading}
            onClick={() => {
              if (modal?.pegboard) {
                savePegboard()
              } else {
                addPegboard()
              }
            }}
            color='darkteal' disabled={isDisabled || title?.trim()?.length < 3 || (isDuplicate && !duplicateName?.name)} text={modal?.pegboard ? 'Save Changes' : 'Create'} />
        </div>
      </div>

    </div>
  )
}

export default CreatePegboardModal