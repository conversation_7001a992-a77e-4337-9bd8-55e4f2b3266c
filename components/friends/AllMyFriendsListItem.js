import React, { useContext } from 'react'
import NameInitials from '../common/NameInitials'
import ReadMore from '../../utils/truncate/readmore'
import DotsIcon from '../icons/DotsIcon'
import OptionsButton from '../chat-v2/common/OptionsButton'
import { useRouter } from 'next/router'
import { UserContext } from '../../pages/_app'
import useThumbnail from '../../hooks/useThumbnail'
import constantOptions from '../../constants/constantOptions'
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus'
import toastNotification from '../../utils/notifications/toastNotification'


export default function AllMyFriendsListItem({
    friendDetails,
    setModal,
    getMyFriendsList,
    isMobile,
    getOneToOneChannel,
    handleCreateRequest,
    isMuted,
    unmutedClubs,
    clubs
}) {

    const { user } = useContext(UserContext)
    const router = useRouter()
    const commonClubObject = user?.clubs?.filter(userClub => unmutedClubs?.includes(userClub?.name));
    const commonClubs = commonClubObject?.map((obj) => obj?.name)
    const clubsForSelection = unmutedClubs?.filter((club) => !commonClubs?.includes(club))
    const { thumbnailUrl } = useThumbnail(friendDetails?.friend_info?.profilePhoto, 128)
    const { maintenanceStatus } = useMaintenanceStatus()

    const renderContactOptions = () => {
        if (friendDetails) {

            let options = {
                Email: () => {
                    window.open(`mailto:${friendDetails?.friend_info?.email}`)
                },
                Edit: () => {
                    setModal({
                        type: "edit-my-contact",
                        contact: friendDetails?.friend_info,
                        width: 780,
                        contactId: friendDetails?.id,
                        getMyFriendsList
                    })
                },
                Delete: () => {
                    setModal({
                        type: 'delete-my-contact',
                        contact: friendDetails?.friend_info,
                        contactId: friendDetails?.id,
                        getMyFriendsList
                    })
                },
            }
            if (!friendDetails?.friend_info?.email) {
                delete options["Email"]
            }
            return (
                <OptionsButton
                    IconComponent={DotsIcon}
                    options={options}
                    left={-30}
                    rotate={true}
                    width={80}
                />
            )
        }
    }
    const renderFriendsOptions = () => {
        let options = {
            ["View Profile"]: () => {
                getOneToOneChannel(friendDetails?.user_id)
            },
            ['Chat']: () => {
                router.push({
                    pathname: `/dashboard/my-tg`,
                    search: `?type=chat&channel_id=${friendDetails?.stream_channel_id}`,
                })
            },
            ['Email']: () => {
                window.location = `mailto:${friendDetails?.friend_info?.email}`
            },
            ['Edit']: () => {
                setModal({
                    type: 'friend-request-notes',
                    title: !friendDetails?.notes
                        ? 'Add a Note'
                        : 'Edit Note',
                    textSize: 24,
                    notes: friendDetails?.notes,
                    friendshipId: friendDetails?.id,
                    getMyFriendsList: getMyFriendsList,
                })
            },
            ['Unfriend']: () => {
                setModal({
                    type: 'unfriend',
                    width: 430,
                    getMyFriendsList: getMyFriendsList,
                    id: friendDetails?.id,
                    friendDetails,
                    isMobile: isMobile,
                })
            },

        }

        if (((clubsForSelection?.length && unmutedClubs) && !isMuted)) {
            options = {
                ...options,
                ['Send Game request']: () => {
                    if (maintenanceStatus?.request) {
                        toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                        })
                    } else {
                        handleCreateRequest("ALL_FRIEND", friendDetails?.friend_id || friendDetails?.user_id, clubsForSelection)
                    }
                },
            }
        }

        return (
            <OptionsButton
                width={143}
                IconComponent={DotsIcon}
                left={-100}
                options={options}
                iconStyle={'mr-md'}
                rotate={true}
            />
        )
    }


    return (
        <>
            <tr
                className={`bg-white text-sm mb-md rounded hover:shadow hover:bg-hover`}
                style={{
                    height: 80,
                    transition: 'opacity 0.2s ease-in-out',
                }}>
                <td className="rounded-l-lg pl-md">
                    <div className="flex items-center py-md">
                        {friendDetails?.friend_info?.profilePhoto ? (
                            <img
                                src={thumbnailUrl}
                                className="mr-md rounded-lg"
                                style={{
                                    height: 36,
                                    width: 36,
                                    objectFit: 'cover',
                                }}
                            />
                        ) : (
                            <div className="mr-md">
                                <NameInitials
                                    contact={{
                                        first_name:
                                            friendDetails?.friend_info?.name,
                                    }}
                                    height={36}
                                    width={36}
                                    fontSize={20}
                                />
                            </div>
                        )}

                        <div className="relative break-words text-14 text-black">
                            {friendDetails?.friend_info?.name}
                        </div>
                        {friendDetails?.isFriend &&
                            <img className='pl-md' src='/svg/tg-logo-2.svg' />
                        }
                    </div>
                </td>
                <td className="pl-md">
                    {friendDetails?.isFriend ? (
                        <>
                            {clubs && (
                                <div className="text-12 text-black">
                                    {clubs &&
                                        clubs[0]}{' '}
                                    {clubs.length > 1 ? (
                                        <span
                                            className="text-darkteal cursor-pointer"
                                            onClick={() =>
                                                setModal({
                                                    type: 'requester-club',
                                                    width: 453,
                                                    clubs:
                                                        friendDetails?.friend_info
                                                            ?.clubs,
                                                })
                                            }>
                                            +
                                            {clubs.length -
                                                1}{' '}
                                            {(clubs.length - 1) < 2 ? 'Other' : 'Others'}
                                        </span>
                                    ) : (
                                        ''
                                    )}
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="text-12 text-black">
                            {friendDetails?.friend_info?.course_name}
                        </div>
                    )}

                </td>
                <td className="pl-md hover:underline text-12 text-black">
                    <a href={`tel:${friendDetails?.friend_info?.phone}`}>
                        {friendDetails?.friend_info?.phone || "-"}
                    </a>
                </td>
                <td className="pl-md cursor-pointer overflow-hidden text-12 text-black">
                    {friendDetails?.friend_info?.email}
                </td>
                <td className="pl-md" style={{ width: 377 }}>
                    <div className="text-12 text-black p-sm rounded-lg hidden md:block break-words">
                        {!friendDetails?.notes ||
                            friendDetails?.notes.trim() === '' ? (
                            '-'
                        ) : friendDetails?.notes &&
                            friendDetails?.notes.length < 40 ? (
                            friendDetails?.notes
                        ) : (
                            <ReadMore className={'text-12'} length={40}>
                                {friendDetails?.notes || ''}
                            </ReadMore>
                        )}
                    </div>
                </td>
                <td className={`pl-md py-md rounded-r-lg relative`}>
                    <div className="mr-md">
                        {friendDetails?.isFriend ? renderFriendsOptions() : renderContactOptions()}
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div className="h-3" />
                </td>
            </tr>
        </>
    )
}