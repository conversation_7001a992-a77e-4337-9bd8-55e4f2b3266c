import React, { useContext, useEffect, useState } from 'react'
import CustomButton from '../buttons/CustomButton'
import { FtrTag } from '../common/FtrTag'
import dateFormatter from '../../utils/helper/dateFormatter'
import ReadMore from '../../utils/truncate/readmore'
import Link from 'next/link'
import { ModalContext } from '../../context/ModalContext'
import NameInitials from '../common/NameInitials'
import useThumbnail from '../../hooks/useThumbnail'
import { DrawerContext } from '../../context/DrawerContext'
import constantOptions from '../../constants/constantOptions'

const ReceivedRequestListItem = ({
    isFtr,
    request,
    refresh,
    hasMessages,
    hasUnreadMessages,
    activeTab,
    showAcceptRequestPopup,
    setShowAcceptRequestPopup,
    fetchTabsCount,
    fetchUnreadMessageStatus,
    allFriendsId,
    requestId,
    setRequestId

}) => {

    const { setDrawer } = useContext(DrawerContext)
    const { setModal } = useContext(ModalContext)
    const [notAcceptableMessage, setNotAcceptableMessage] = useState(false);
    const { thumbnailUrl } = useThumbnail(request?.requestor_profile_photo, 128)
    let chatInitiated = request?.has_messages;
    let chatInitiatedFromRequester = request?.requester_has_message;
    const [showToolTip, setShowToolTip] = useState(false)
    const [declineButtonColor, setDeclineButtonColor] = useState("lightestgray");
    const { GAME_INFO } = constantOptions?.DRAWER_TYPE


    useEffect(() => {
        if (notAcceptableMessage) {
            setTimeout(
                () => setNotAcceptableMessage(false),
                3000
            );
        }
    }, [notAcceptableMessage])

    useEffect(() => {
        if (requestId && request?.request_id === requestId) {
            setDrawer({
                type: GAME_INFO,
                global: true,
                requestId: request?.request_id,
                received: true,
                isFtr,
                gameId: request?.game_id,
                setRequestId,
                fetchTabsCount,
                fetchUnreadMessageStatus,
                refresh
            })
        }
    }, [requestId])

    return (
        <div
            className="flex justify-between p-md bg-white rounded-lg border border-lightestgray mb-12 relative shadow-lg cursor-pointer border border-lightestgray border-[1.5px]"
            onClick={(e) => {
                // Check if the click is on a button or a child of a button
                if (e.target.closest('button') || e.target.closest('a')) {
                    return;
                }

                setDrawer({
                    type: GAME_INFO,
                    global: true,
                    requestId: request?.request_id,
                    received: true,
                    isFtr,
                    gameId: request?.game_id,
                    setRequestId,
                    fetchTabsCount,
                    fetchUnreadMessageStatus,
                    refresh
                });
            }}
        >
            <div>
                {isFtr ? (<FtrTag />) : null}
                {/* Request ID and Close button */}
                <div className="flex mb-sm">
                    <div className="pt-xs mr-sm">
                        <img src="/svg/golf-post-small.svg" />
                    </div>
                    <div className="flex flex-col">
                        <div className="text-18 font-medium">{request?.club_name}</div>
                        <div className="text-12 text-grayLight">
                            {dateFormatter(request?.start_date, request?.end_date)}
                        </div>
                    </div>
                </div>

                {/* Request message */}
                <div className="mb-md">
                    <p className="text-14" >
                        {!request?.message ? (
                            "-"
                        ) : request?.message &&
                            request?.message.length < 350 ? (
                            request?.message
                        ) : (
                            <ReadMore length={350}>
                                {request?.message || ''}
                            </ReadMore>
                        )}
                    </p>
                </div>

                {/* Accompanied play tag */}
                {request?.accompanied_only ?
                    <div className="mb-md">
                        <span className="font-medium">Accompanied play only</span>
                    </div> : null}

                {/* Requester info */}
                <div className="flex items-center mb-md">
                    {!request?.requestor_profile_photo ? (
                        <NameInitials rounded={'full'} height={24} width={24} user={{ first_name: request?.requestor_full_name }} fontSize={16} />
                    ) : (
                        <div className="w-[24px] h-[24px] rounded-full mr-xs"
                            style={{
                                backgroundImage: `url("${thumbnailUrl}")`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center',
                            }}
                        >
                        </div>
                    )}
                    <span className="font-medium ml-sm">{request?.requestor_full_name}</span>
                    {request?.number_of_players > 1 &&
                        <span className="ml-xs">+{request?.number_of_players - 1}</span>
                    }
                </div>

            </div>

            <div className="flex flex-col justify-between">
                <div className="flex items-center justify-end">
                    <div>#{request?.game_id}</div>
                    <div>
                        <CustomButton
                            height={32}
                            width={32}
                            buttonImage={declineButtonColor === "red" ? "/svg/Cross-Icon-White.svg" : "/svg/Cross-Icon-Gray.svg"}
                            color={declineButtonColor}
                            imageRightMargin={0}
                            imageMarginBottom='0'
                            onMouseEnter={() => setDeclineButtonColor("red")}
                            onMouseLeave={() => setDeclineButtonColor("lightestgray")}
                            onClick={() => {
                                setModal({
                                    type: 'confirm-request',
                                    subType: 'decline',
                                    width: 451,
                                    img: {
                                        backgroundHeight: '80px',
                                        backgroundWidth: '80px',
                                        backgroundColor: 'tealTierBg',
                                        src: '/svg/Golf-Post-Big.svg',
                                    },
                                    request,
                                    refresh,
                                    requestType: "received/open",
                                    fetchUnreadMessageStatus,
                                    fetchTabsCount,
                                    declineReasonMandatory: (chatInitiated),
                                    decliningRequest: true

                                })
                            }}
                        />
                    </div>
                </div>

                <div className="flex flex-col justify-between">
                    {showToolTip ?
                        <div className={`absolute text-12 font-thin rounded shadow-lg bg-lightestgray text-gray p-sm ${!chatInitiated && !chatInitiatedFromRequester ? 'top-[50px]' : 'top-[0px]'} right-[170px] max-w-[200px]`} >{showToolTip}</div>
                        : null}
                    <div className=""
                        onMouseEnter={() => {
                            if (!chatInitiated && !chatInitiatedFromRequester) { setShowToolTip('Start chat to accept the Request') }
                            else if (chatInitiated && !chatInitiatedFromRequester) { setShowToolTip('You need to confirm the logistics with the requester via chat here before you can Accept') }
                        }}
                        onMouseLeave={() => setShowToolTip(false)}
                    >
                        <CustomButton
                            text="Accept"
                            width={138}
                            height={40}
                            textSize="12"
                            tip={'Start chat to accept the Request'}
                            chatInitiated={chatInitiated}
                            chatInitiatedFromRequester={chatInitiatedFromRequester}
                            disabled={!chatInitiated || !chatInitiatedFromRequester}
                            cssClassName='mb-sm'
                            color='green'
                            onClick={() => {
                                if (chatInitiated && chatInitiatedFromRequester) {
                                    if (showAcceptRequestPopup) {
                                        setModal({
                                            title: '',
                                            type: 'request-accept',
                                            width: 550,
                                            request,
                                            refresh,
                                            setShowAcceptRequestPopup: (val) => setShowAcceptRequestPopup(val)
                                        })
                                    } else {
                                        setModal({
                                            type: 'confirm-request',
                                            img: {
                                                backgroundHeight: '80px',
                                                backgroundWidth: '80px',
                                                backgroundColor: 'tealTierBg',
                                                src: '/svg/Golf-Post-Big.svg',
                                            },
                                            request,
                                            refresh,
                                            fetchUnreadMessageStatus,
                                            fetchTabsCount,
                                            width: 451,
                                        })
                                    }
                                } else {
                                    setNotAcceptableMessage(request.request_id);
                                }
                            }}
                        />
                    </div>
                    <Link
                        shallow={true}
                        href={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || request?.stream_channel_id ? "-chat" : ""}/[request_id]`}
                        as={`/dashboard/request${(!request?.stream_channel_id && !request?.sendbird_channel_id) || request?.stream_channel_id ? "-chat" : ""}/${request.request_id}`}>
                        <div className="relative">
                            <CustomButton
                                width={138}
                                height={40}
                                textSize="12"
                                text="Chat"
                                imageRightMargin={5}
                                color={hasMessages ? 'darkteal' : 'tealTierBg'}
                                textColor={hasMessages ? 'white' : 'darkteal'}
                                borderColor={hasMessages ? '' : 'darkteal'}
                                buttonImage={hasMessages ? '/svg/Message-Icon-White.svg' : "/svg/Message-Icon.svg"}
                            />
                            {hasUnreadMessages ?
                                <div className='absolute h-[6px] w-[6px] rounded-full bg-darkteal right-[9px] top-[1px] outline outline-1 outline-white'></div>
                                : null}
                        </div>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default ReceivedRequestListItem
