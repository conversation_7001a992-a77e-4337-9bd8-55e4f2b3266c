import React, { useContext, useEffect, useRef, useState } from 'react'
import LeaderboardIcon from '../icons/LeaderboardIcon';
import { ModalContext } from '../../context/ModalContext';
import { DrawerContext } from '../../context/DrawerContext';
import { useRouter } from 'next/router';
import { CustomStreamContext } from '../../context/CustomStreamContext';
import constantOptions from '../../constants/constantOptions';
import { UserContext } from '../../pages/_app';
import ThreeDotsIcon from '../icons/ThreeDotsIcon-2';
import OptionsButton from '../chat-v2/common/OptionsButton';

const PegboardListItem = ({ loading, pegboard, setRefresh, setUpdatedRank, updatePegboardRank, setSelectedPegboard, isUser, userId, hiddenPegboards, togglePegboardVisibility }) => {
    const router = useRouter()
    const { user } = useContext(UserContext)
    const { setModal } = useContext(ModalContext);
    const { setDrawer } = useContext(DrawerContext)
    const { setOtherUserId } = useContext(CustomStreamContext)
    const { GLOBAL_PROFILE_INFO } = constantOptions?.DRAWER_TYPE
    const [isHovered, setIsHovered] = useState(false);

    const containerRef = useRef(null);
    const customButtonRef = useRef(null);
    const linkRef = useRef(null);

    const preventDrawer = [user?.id, constantOptions?.ADMIN_ID].includes(pegboard?.creatorId)

    useEffect(() => {
        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, []);

    const openPegboard = () => {
        setSelectedPegboard(pegboard)
        router.push({
            pathname: `/dashboard/play/`,
            search: `?type=pegboards&${router?.query?.hidden ? 'hidden=true' : ''}&id=${pegboard?.id}`,
        })
    };

    const handleClickOutside = (event) => {
        if (containerRef.current && containerRef.current.contains(event.target)
            && customButtonRef.current && !customButtonRef.current.contains(event.target)
            && linkRef.current && !linkRef.current.contains(event.target)
        ) {
            openPegboard();
        }
    };

    return (
        <>
            <tr
                onMouseLeave={() => {
                    setIsHovered(false)
                }}
                ref={containerRef} draggable={!isUser ? true : false}
                onDragOver={(e) => e.preventDefault()}
                onDragEnter={() => {
                    if (!isUser) {
                        setUpdatedRank(pegboard?.order)
                    }
                }}
                onDragEnd={() => {
                    if (!isUser) {
                        updatePegboardRank(pegboard?.id)
                    }
                }}
                className={`bg-white text-sm mb-md rounded hover:shadow cursor-pointer rounded-lg`}
                style={{
                    height: 68,
                    transition: 'opacity 0.2s ease-in-out',
                }}>
                <td className="pl-md rounded-l-lg">
                    <div className="text-12 text-black">
                        {pegboard?.name}
                    </div>
                </td>
                <td className="">
                    {pegboard?.totalPlayedClubs}/{pegboard?.totalClubs}
                </td>
                <td className="capitalize">
                    <div ref={linkRef} className={`cursor-pointer w-max ${!preventDrawer && "hover:underline hover:text-darkteal"}`}
                        onClick={() => {
                            if (!preventDrawer) {
                                setOtherUserId(pegboard?.creatorId)
                                setDrawer({
                                    type: GLOBAL_PROFILE_INFO,
                                    global: true
                                })
                            }

                        }}
                    >
                        {pegboard?.creatorName}
                    </div>
                </td>
                <td className={`pl-md rounded-r-lg pr-md`} style={{ textAlign: 'right' }}>
                    <div className='flex rounded-r-lg justify-end' ref={customButtonRef}>
                        {
                            !pegboard?.isPrivate &&
                            <div className='h-[30px] w-[30px] rounded-lg bg-lightestgray hover:bg-tealTierBg rounded-lg bg-lightestgray text-gray hover:bg-darkteal hover:text-white cursor-pointer flex-center mr-sm'
                                style={{ height: 30, width: 30 }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setDrawer({
                                        type: 'leaderboard',
                                        global: true,
                                        pegboardId: pegboard?.id,
                                        creatorId: pegboard?.creatorId,
                                        totalPegboardClubs: pegboard?.totalClubs || 0
                                    })
                                }}
                            >
                                <LeaderboardIcon />
                            </div>
                        }
                        {userId !== pegboard?.creatorId
                            &&
                            <OptionsButton
                                IconComponent={(props) => <ThreeDotsIcon {...props} />}
                                iconStyle="rounded-lg bg-lightestgray "
                                top={35}
                                left={-100}
                                width={router?.query?.hidden ? 120 : 105}

                                options={
                                    router?.query?.hidden ? {
                                        'Unhide Pegboard': () => {
                                            togglePegboardVisibility(pegboard?.id)
                                        }
                                    } : {
                                        'Hide Pegboard': () => {
                                            setModal({
                                                type: "confirmation-modal",
                                                text: "Are you sure you want to hide this pegboard from your profile?",
                                                buttonColor: "darkteal",
                                                pegboard,
                                                loading: loading,
                                                onClick: () => {
                                                    togglePegboardVisibility(pegboard?.id)
                                                }
                                            })
                                        }
                                    }

                                }
                            />
                            // <div
                            //     className='relative'
                            //     onMouseEnter={() => setIsHovered(true)}
                            // >
                            //     <div className='h-[30px] w-[30px] rounded-lg bg-lightestgray hover:bg-tealTierBg rounded-lg bg-lightestgray text-gray hover:bg-darkteal hover:text-white cursor-pointer flex-center mr-sm'
                            //         style={{ height: 30, width: 30 }}
                            //         onClick={(e) => {
                            //             e.stopPropagation();
                            //         }}
                            //     >
                            //         <ThreeDotsIcon isActive={isHovered} />
                            //     </div>
                            //     {isHovered && (
                            //         <div
                            //             className='absolute hover:bg-tealTierBg top-[35px] left-[-100px] h-[28px] w-[106px] flex-center text-black bg-white rounded-lg border border-[1px] border-lightestgray cursor-pointer'
                            //             onClick={(e) => {
                            //                 e.stopPropagation();
                            //                 togglePegboardVisibility(pegboard?.id)
                            //             }}
                            //         >
                            //             {router?.query?.hidden ? 'Unhide Pegboard' : 'Hide Pegboard'}
                            //         </div>
                            //     )}
                            // </div>
                        }
                        {userId === pegboard?.creatorId ? (
                            <div onClick={(e) => {
                                e.stopPropagation()
                                setModal({
                                    type: "create-pegboard",
                                    title: "Edit Pegboard",
                                    width: "829px",
                                    pegboard,
                                    setRefresh
                                })
                            }}
                                className="rounded-lg bg-lightestgray text-gray hover:bg-darkteal hover:text-white cursor-pointer flex-center mr-sm"
                                style={{ height: 30, width: 30 }}>
                                <img src='/svg/EditIconBlack.svg' />
                            </div>
                        ) : (
                            null
                        )}
                        {
                            userId === pegboard?.creatorId ? (
                                <div onClick={(e) => {
                                    e.stopPropagation()
                                    setModal({
                                        type: "delete-pegboard-confirmation",
                                        title: "Delete Pegboard",
                                        pegboard,
                                        setRefresh
                                    })
                                }}
                                    style={{ height: 30, width: 30 }}
                                    className="rounded-lg bg-lightestgray text-gray hover:bg-darkteal hover:text-white hover:bg-red cursor-pointer flex-center mr-sm"
                                >
                                    <img src='/svg/DeleteIconBlack.svg' />
                                </div>
                            ) : (
                                null
                            )
                        }
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div className="h-3" />
                </td>
            </tr>
        </>
    )
}

export default PegboardListItem