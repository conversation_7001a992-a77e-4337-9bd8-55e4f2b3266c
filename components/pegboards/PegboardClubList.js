import React, { useContext, useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import constantOptions from '../../constants/constantOptions'
import CustomButton from '../buttons/CustomButton'
import { UserContext } from '../../pages/_app'
import ENDPOINTS from '../../constants/endpoints.json'
import PegboardClubMobileListItem from './PegboardClubMobileListItem'
import { UPDATE_USER } from '../../graphql/mutations/user'
import toastNotification from '../../utils/notifications/toastNotification'
import PegboardHeader from './PegboardHeader'
import useClient from '../../graphql/useClient'
import PegboardClubsListItem from './PegboardClubsListItem'
import MESSAGES from '../../constants/messages'
import { ThreeDots } from 'react-loader-spinner'



const PegboardClubList = ({
  setRefresh,
}) => {
  const router = useRouter()
  const { isMobile } = useCheckDeviceScreen()
  const client = useClient()

  const { USER_PEGBOARD_DETAILS_HEADER } = constantOptions
  const { user, token, fetchUser } = useContext(UserContext)
  const [creatorMode, setCreatorMode] = useState(false)
  const [pegboardDetails, setPegboardDetails] = useState([])
  const [clubsList, setClubsList] = useState([])
  const [newClubRank, setNewClubRank] = useState(0)
  const [newClub, setNewClub] = useState({})
  const [clubSearchValue, setClubSearchValue] = useState('')
  const [editable, setEditable] = useState(0)
  const [isCreator, setIsCreator] = useState(false)
  const [selectedPegboard, setSelectedPegboard] = useState(null)
  const [playedClubs, setPlayedClubs] = useState([])
  const [pegboardId, setPegboardId] = useState(null)
  const [loading, setLoading] = useState(false)


  useEffect(() => {
    fetchPlayedClubs()
    setPegboardId(router?.query?.id)
    fetchPegboardDetails(router?.query?.id)
  }, [])

  useEffect(() => {
    if (creatorMode) {
      const updatedDetails = [...pegboardDetails, { add: true }]
      setClubsList(updatedDetails)
    } else {
      setClubsList(pegboardDetails)
    }
  }, [creatorMode, pegboardDetails])

  useEffect(() => {
    if (selectedPegboard?.creator_id === user?.id || selectedPegboard?.creatorId === user?.id) {
      setIsCreator(true)
    } else {
      setIsCreator(false)
    }
  }, [selectedPegboard, user?.id])

  const fetchPlayedClubs = async () => {
    try {
      await fetchUser();
      setPlayedClubs(user?.playedClubs || [])
    } catch (error) {
      console.error(error)
    }
  }

  const fetchPegboardDetails = async (pegboardId = selectedPegboard?.id) => {
    setLoading(true)
    try {
      await fetch(ENDPOINTS.GET_PEGBOARD_DETAILS_V3, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({ userId: user?.id, pegboardId }),
      })
        .then((data) => data.json())
        .then(({ data }) => {
          if (!data?.pegboard) {
            router.push({ pathname: '/dashboard/errors/404' })
          } else {
            setSelectedPegboard(data?.pegboard)
            setPegboardDetails(data?.pegboardClubs);
            if (data?.pegboard?.isHidden) {
              router.push({
                pathname: '/dashboard/play',
                query: { type: 'pegboards', hidden: true, id: data?.pegboard?.id }
              })
            }
            setClubSearchValue('')
          }

        })
      setLoading(false)
    } catch (error) {
      console.error(error)
    }
  }

  const addPegboardClub = async (clubId = newClub?.id) => {
    setLoading(true)
    try {
      await fetch(ENDPOINTS.ADD_PEGBOARD_CLUB, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          pegboardId: selectedPegboard?.id,
          clubId: clubId
        }),
      })
        .then((data) => data.json())
        .then((data) => {
          fetchPegboardDetails()
          setClubSearchValue('')
          setRefresh((prev) => prev + 1)
          setNewClub({})

        })
      setLoading(false)
    } catch (error) {
      console.error(error)
    }
  }

  const handleUpdate = async (clubId) => {
    setLoading(true)
    const inPlayedClubs = playedClubs.includes(clubId)
    const updatedClubs = inPlayedClubs ? playedClubs.filter((id) => id !== clubId) : [...playedClubs, clubId]
    await client
      .request(UPDATE_USER, {
        user_id: user?.id,
        user: { playedClubs: updatedClubs },
      })
      .then(() => setPlayedClubs([...updatedClubs]))
      .catch(() => toastNotification({ type: constantOptions.TOAST_TYPE.ERROR, message: MESSAGES[500] }))
      .finally(() => setLoading(false))
  }

  const updateClubRank = async (clubId) => {
    if (newClubRank > pegboardDetails.length) {
      toastNotification({
        type: constantOptions.TOAST_TYPE.ERROR,
        message: MESSAGES?.PEGBOARDS?.VALID_RANK
      })
    } else {
      setLoading(true)
      await fetch(ENDPOINTS?.USER_UPDATE_PEGBOARD_CLUB_RANK, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          pegboardId: selectedPegboard?.id,
          clubId,
          newRank: newClubRank
        })
      })
        .then((response) => response.json())
        .then((data) => {
          fetchPegboardDetails()
          setEditable((prev) => prev + 1)
        })
      setLoading(false)
    }
  }


  return (
    <div className="flex flex-col w-full items-center px-md md:px-lg relative">
      <div className='flex justify-between w-full items-center pt-md'>
        <div className='text-16 lg:text-20 lg:font-medium'>{router?.query?.hidden ? "Hidden Pegboard" : "Pegboard"}</div>
      </div>
      <div className='flex w-full items-center'>
        <div className={`flex items-center ${isMobile ? "w-full" : ""}`}>
          <div className={`flex cursor-pointer ${isMobile ? "flex-1" : ""}`}
            onClick={() => {
              router.back()
              setCreatorMode(false)
              setPegboardId()
              setSelectedPegboard()
              setNewClub()
              setRefresh((prev) => prev + 1) // so that we can call the get pegboards API once again
            }}
          >
            <img className='mr-sm' width={15} src='/svg/BackArrowBlack.svg' />
            <div className={`text-20 ${isMobile ? "inline" : ""}`}>{selectedPegboard?.name}</div>
          </div>
          {creatorMode &&
            <div className='h-[22px] w-[81px] flex-center rounded-lg text-10 bg-lighttealbackground2 text-darkteal ml-md'>
              Creator Mode
            </div>}
        </div>
        <div className='flex-1 flex justify-end'>
          {(selectedPegboard?.creatorId || selectedPegboard?.creator_id) === user?.id &&
            <>
              {!isMobile ? (
                <>
                  {(!pegboardDetails?.length) ?
                    <CustomButton
                      text={creatorMode ? 'Save Pegboard' : 'Add Clubs'}
                      width={isMobile ? 115 : 103}
                      height={32}
                      onClick={() => {
                        setCreatorMode((prev) => isCreator && !prev)
                      }} /> :
                    <CustomButton
                      text={creatorMode ? 'Save Pegboard' : 'Edit Clubs'}
                      buttonImage={creatorMode ? '' : '/svg/EditIconBlack.svg'}
                      color={creatorMode ? 'darkteal' : 'lightestgray'}
                      textColor={creatorMode ? 'white' : 'black'}
                      width={isMobile ? 103 : 115}
                      height={32}
                      onClick={() => {
                        setCreatorMode((prev) => isCreator && !prev)
                      }} />
                  }
                </>
              ) : (
                null
              )}
            </>
          }
        </div>
      </div>
      {(selectedPegboard?.creatorId || selectedPegboard?.creator_id) === user?.id &&
        <>
          {isMobile ? (
            <div className='justify-between flex items-center w-full py-sm'>
              <div className='text-16'>
                Club{pegboardDetails?.length > 1 ? "s" : ""}({pegboardDetails?.length})
              </div>
              {(!pegboardDetails?.length) ?
                <>
                  {!creatorMode ? (
                    <div
                      onClick={() => {
                        setCreatorMode((prev) => !prev)
                      }}
                      className='text-darkteal'>Add Clubs</div>
                  ) : (
                    <CustomButton
                      marginX='0'
                      text={'Save Pegboard'}
                      width={115}
                      height={32}
                      onClick={() => {
                        setCreatorMode((prev) => !prev)
                      }} />
                  )}
                </>
                :
                <CustomButton
                  marginX='0'
                  text={creatorMode ? 'Save Pegboard' : 'Edit Clubs'}
                  buttonImage={creatorMode ? '' : '/svg/EditIconBlack.svg'}
                  color={creatorMode ? 'darkteal' : 'lightestgray'}
                  textColor={creatorMode ? 'white' : 'black'}
                  width={115}
                  height={32}
                  onClick={() => {
                    setCreatorMode((prev) => isCreator && !prev)
                  }} />
              }
            </div>
          ) : (
            null
          )}
        </>
      }
      {loading &&
        <div className="absolute min-h-screen h-full w-full flex-center backdrop-blur-sm" style={{ zIndex: 1000 }}>
          <ThreeDots
            visible={true}
            height="50"
            width="50"
            color={"#098089"}
            radius="9"
            ariaLabel="three-dots-loading"
            wrapperStyle={{}}
            wrapperClass=""
          />
        </div>}
      {(!pegboardDetails?.length && !creatorMode) ? (
        <div className='flex flex-col flex-center h-[500px]'>
          {
            (isCreator) ? <>
              <div className='text-center'>You don't have any clubs added to this Pegboard yet. Start adding clubs now</div>
              <div
                className="text-darkteal cursor-pointer"
                onClick={() => {
                  setCreatorMode((prev) => isCreator && !prev)
                }}>+ Add club
              </div>
            </> :
              <div className='text-center'>There are no clubs in this Pegboard yet.</div>
          }

        </div>
      ) : (
        <>
          {isMobile ? (<>
            {clubsList?.map((club) => {
              return (
                <PegboardClubMobileListItem
                  user={user}
                  key={club?.id}
                  setRefresh={setRefresh}
                  source={'pegboard'}
                  club={club}
                  clubs={pegboardDetails?.map((t) => t?.clubId)}
                  pegboardId={selectedPegboard?.id}
                  isCreator={selectedPegboard?.creatorId === user?.id}
                  fetchPegboardDetails={fetchPegboardDetails}
                  handleUpdate={handleUpdate}
                  creatorMode={creatorMode}
                  updateClubRank={updateClubRank}
                  setNewClubRank={setNewClubRank}
                  newClubRank={newClubRank}
                  selectedPegboard={selectedPegboard}
                  pegboard={selectedPegboard}
                  savePegboardClub={addPegboardClub}
                  newClub={newClub}
                  clubSearchValue={clubSearchValue}
                  setClubSearchValue={setClubSearchValue}
                  editable={editable}
                  setCreatorMode={setCreatorMode}
                  setPegboardId={setPegboardId}
                  setSelectedPegboard={setSelectedPegboard}
                  setNewClub={setNewClub}
                  playedClubs={playedClubs || []}
                />
              )
            })}
          </>) : (<>
            <table className="relative w-full mt-md" onDragOver={(e) => e.preventDefault()}>
              <PegboardHeader headers={creatorMode ? USER_PEGBOARD_DETAILS_HEADER.filter((label) => !['Friends Played', 'Mark as Played'].includes(label)) : USER_PEGBOARD_DETAILS_HEADER} />
              <tbody className='w-full' onDragOver={(e) => e.preventDefault()}>
                {clubsList?.map((club) => {
                  return (
                    <PegboardClubsListItem
                      key={`${club?.club?.id}_${club?.rank}`}
                      setRefresh={setRefresh}
                      source={'pegboard'}
                      club={club}
                      clubs={pegboardDetails?.map((t) => t?.clubId)}
                      pegboardId={selectedPegboard?.id}
                      isCreator={selectedPegboard?.creatorId === user?.id}
                      fetchPegboardDetails={fetchPegboardDetails}
                      handleUpdate={handleUpdate}
                      creatorMode={creatorMode}
                      updateClubRank={updateClubRank}
                      setNewClubRank={setNewClubRank}
                      newClubRank={newClubRank}
                      selectedPegboard={selectedPegboard}
                      playedClubs={playedClubs || []}
                      pegboard={selectedPegboard}
                      savePegboardClub={addPegboardClub}
                      newClub={newClub}
                      clubSearchValue={clubSearchValue}
                      setClubSearchValue={setClubSearchValue}
                      editable={editable}
                      setCreatorMode={setCreatorMode}
                      setPegboardId={setPegboardId}
                      setSelectedPegboard={setSelectedPegboard}
                      setNewClub={setNewClub}
                      clubsLength={clubsList?.length}
                    />)
                })}
              </tbody>
            </table>
          </>)}
        </>
      )}
    </div>
  )
}

export default PegboardClubList