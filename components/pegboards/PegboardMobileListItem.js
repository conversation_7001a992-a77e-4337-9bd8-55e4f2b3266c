import React, { useContext } from 'react'
import OptionsButton from '../chat-v2/common/OptionsButton'
import { useRouter } from 'next/router';
import { ModalContext } from '../../context/ModalContext';
import { DrawerContext } from '../../context/DrawerContext';


const PegboardMobileListItem = ({ pegboard, setRefresh, setSelectedPegboard, userId, togglePegboardVisibility, loading }) => {
    const router = useRouter()
    const { modal, setModal } = useContext(ModalContext)
    const { drawer, setDrawer } = useContext(DrawerContext)

    const renderOptions = () => {
        let options = {}

        if (!pegboard?.isPrivate) {
            options = {
                ...options,
                ['View Leaderboard']: () => {
                    setDrawer({
                        type: 'leaderboard',
                        global: true,
                        pegboardId: pegboard?.id,
                        creatorId: pegboard?.creatorId,
                        totalPegboardClubs: pegboard?.totalClubs || 0
                    })
                },
            }
        }

        // Add Hide/Unhide option based on router query
        if (router?.query?.hidden) {
            options['Unhide Pegboard'] = () => togglePegboardVisibility(pegboard?.id)
        } else {
            options['Hide Pegboard'] = () => {
                setModal({
                    type: "confirmation-modal",
                    text: "Are you sure you want to hide this pegboard from your profile?",
                    buttonColor: "darkteal",
                    pegboard,
                    loading: loading,
                    onClick: () => {
                        togglePegboardVisibility(pegboard?.id)
                    }
                })
            }
        }

        if (userId === pegboard?.creatorId) {
            options = {
                ...options,
                ['Edit']: () => {
                    setModal({
                        type: "create-pegboard",
                        title: "Edit Pegboard",
                        width: "829px",
                        pegboard,
                        setRefresh
                    })
                },
                ['Delete']: () => {
                    setModal({
                        type: "delete-pegboard-confirmation",
                        title: "Delete Pegboard",
                        pegboard,
                        setRefresh
                    })
                }
            }
        }

        return (
            <OptionsButton
                IconComponent={() => <img src='/svg/DotsIconBlack.svg' />}
                left={-80}
                options={options}
                width={120}
            />
        )
    }

    return (
        <div className='bg-white rounded-lg p-md mb-sm w-full flex'
            onClick={() => {
                setSelectedPegboard(pegboard)
                router.push({
                    pathname: `/dashboard/play/`,
                    search: `?type=pegboards&${router?.query?.hidden ? 'hidden=true' : ''}&id=${pegboard?.id}`,
                })
            }}
        >
            <div className='flex-1 flex'>
                <div style={{ minHeight: 20, minWidth: 20 }}><img src='/svg/GolfPostBlack.svg' /></div>
                <div className='flex-col pl-md'>
                    <div className='text-16'>{pegboard?.name}</div>
                    <div className='text-12 text-gray py-xs'>Created By - <span className='text-black'>{pegboard?.firstName}{' '}{pegboard?.lastName}</span> </div>
                    <div className='text-12 text-gray'>Total: <span className='text-black'>{pegboard?.totalClubs}</span> | Played: <span className='text-black'>{pegboard?.totalPlayedClubs}</span></div>
                </div>
            </div>
            <div className='flex flex-1 justify-end'>
                {renderOptions()}
            </div>
        </div>
    )
}

export default PegboardMobileListItem