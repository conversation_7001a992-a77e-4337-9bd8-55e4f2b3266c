import React, { useState, useContext, useEffect } from 'react'
import ENDPOINTS from '../../constants/endpoints.json'
import { UserContext } from '../../pages/_app'
import constantOptions from '../../constants/constantOptions'
import { ThreeDots } from 'react-loader-spinner'
import { useRouter } from 'next/router'
import CustomButton from '../buttons/CustomButton'
import OptionsButton from '../chat-v2/common/OptionsButton'
import ThreeDotsIcon from '../icons/ThreeDotsIcon'
import ModalContext from '../../context/ModalContext'
import toastNotification from '../../utils/notifications/toastNotification'
import PegboardMobileListItem from './PegboardMobileListItem'
import PegboardHeader from './PegboardHeader'
import PegboardListItem from './PegboardListItem'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'

const PegboardList = ({
  setModal,
  setRefresh,
  setOpenNewPegboard,
  setSelectedPegboard,
  selectedPegboard,
}) => {

  const { user, token } = useContext(UserContext)
  const router = useRouter()
  const [pegboards, setPegboards] = useState([])
  const [pegboardsForDuplicate, setPegboardsForDuplicate] = useState([])
  const [loading, setLoading] = useState(false)
  const { isMobile } = useCheckDeviceScreen()
  const { USER_PEGBOARD_HEADER } = constantOptions

  useEffect(() => {
    fetchPegboards()
  }, [router?.query?.hidden])

  const togglePegboardVisibility = async (pegboardId) => {
    setLoading(true)
    const isCurrentlyHidden = router?.query?.hidden === 'true'
    try {
      await fetch(ENDPOINTS.TOGGLE_PEGBOARD_VISIBILITY, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({
          pegboardId,
          isHidden: !isCurrentlyHidden,
          userId: user?.id
        }),
      })
        .then((data) => data.json())
        .then((data) => {
          fetchPegboards(isCurrentlyHidden)
          setRefresh((prev) => prev + 1)
          toastNotification({
            type: constantOptions.TOAST_TYPE.PEGBOARD_CREATED,
            message: isCurrentlyHidden ? 'Pegboard restored to your list' : 'Pegboard hidden successfully'
          })
          setLoading(false)
          setModal()
        })
    } catch (error) {
      console.error(error)
      setLoading(false)
    }
  }

  const fetchPegboards = async () => {
    setLoading(true)
    try {
      await fetch(ENDPOINTS.GET_PEGBOARDS, {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          ['Content-Type']: 'application/json',
          ['Authorization']: `Bearer ${token}`,
        },
        body: JSON.stringify({ userId: user?.id, hidden: router?.query?.hidden }),
      })
        .then((data) => data.json())
        .then((data) => {
          setPegboards(data.data);
          setPegboardsForDuplicate(data?.data.filter((t) => [user?.id, constantOptions?.ADMIN_ID].includes(t.creatorId)));
        })
      setLoading(false)
    } catch (error) {
      console.error(error)
      setLoading(false)
    }
  }
  return (
    <div className="flex flex-col w-full items-center px-md md:px-lg relative">
      {loading &&
        <div className="absolute min-h-screen h-full w-full flex-center backdrop-blur-sm" style={{ zIndex: 1000 }}>
          <ThreeDots
            visible={true}
            height="50"
            width="50"
            color={"#098089"}
            radius="9"
            ariaLabel="three-dots-loading"
            wrapperStyle={{}}
            wrapperClass=""
          />
        </div>}
      <div className='w-full flex justify-between items-center pt-md'>
        <div className='flex items-center gap-2'>
          {(router?.query?.hidden && router?.query?.id === undefined) && <img className='cursor-pointer' onClick={() => {
            router.push({
              pathname: `/dashboard/play/`,
              search: `?type=pegboards`,
            })
          }} src="/svg/BackArrowBlack.svg" />}
          <div className='text-16 lg:text-20 lg:font-medium'>{(router?.query?.hidden) ? 'Hidden Pegboards' : 'Pegboards'}</div>
        </div>
        {
          <div className='flex items-center gap-2'>
            {(router?.query?.hidden) ? null :
              <CustomButton marginX='0' width={158} height={32} text="Create New Pegboard"
                onClick={() => setModal({
                  type: "create-pegboard",
                  title: "Create Pegboard",
                  width: "829px",
                  setRefresh,
                  pegboards: pegboardsForDuplicate,
                  setOpenNewPegboard: setOpenNewPegboard
                })} />
            }
            {(router?.query?.hidden === undefined) &&
              <OptionsButton
                IconComponent={(props) => <ThreeDotsIcon {...props} />}
                iconStyle="rounded-full"
                top={35}
                left={-160}
                width={150}

                options={{
                  'View Hidden Pegboard': () => {
                    router.push({
                      pathname: `/dashboard/play/`,
                      search: `?type=pegboards&hidden=true`,
                    })
                  }
                }}
              />
            }
          </div>
        }
      </div>
      <>{isMobile ? (
        <div className='mt-md w-full'>
          {pegboards?.length > 0 ? (
            pegboards?.map((pegboard) => {
              return (
                <PegboardMobileListItem
                  key={pegboard.id}
                  setRefresh={setRefresh}
                  pegboard={pegboard}
                  pegboards={pegboards}
                  setSelectedPegboard={setSelectedPegboard}
                  selectedPegboard={selectedPegboard}
                  isUser={user?.role === 'user'}
                  userId={user?.id}
                  togglePegboardVisibility={togglePegboardVisibility}
                  loading={loading}
                />
              )
            })
          ) : (
            <div className="flex flex-col items-center justify-center h-[400px] w-full">
              <img src="/svg/EmptyState.svg" alt="No pegboards" className="w-20 h-20 mb-4" />
              <div className="text-16 text-gray-500">
                {router?.query?.hidden ? "No Pegboards Hidden!" : "No pegboards available"}
              </div>
            </div>
          )}
        </div>
      ) : (
        <>
          {pegboards?.length > 0 ? (
            <table className="relative w-full mt-md" onDragOver={(e) => e.preventDefault()}>
              <PegboardHeader headers={USER_PEGBOARD_HEADER} />
              <tbody onDragOver={(e) => e.preventDefault()}>
                {pegboards?.map((pegboard) => {
                  return (
                    <PegboardListItem
                      loading={loading}
                      togglePegboardVisibility={togglePegboardVisibility}
                      key={pegboard?.id}
                      setRefresh={setRefresh}
                      pegboard={pegboard}
                      pegboards={pegboards}
                      setSelectedPegboard={setSelectedPegboard}
                      selectedPegboard={selectedPegboard}
                      isUser={user?.role === 'user'}
                      userId={user?.id}
                    />
                  )
                })}
              </tbody>
            </table>
          ) : (
            <div className="flex flex-col items-center justify-center h-[400px] w-full">
              <div className="text-16 text-gray-500">
                {router?.query?.hidden ? "No Pegboards Hidden!" : "No pegboards available"}
              </div>
            </div>
          )}
        </>
      )}
      </>
    </div>)
}

export default PegboardList