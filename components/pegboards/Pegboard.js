import React, { useContext, useState, useEffect, use } from 'react'
import { UserContext } from '../../pages/_app'
import { ThreeDots } from 'react-loader-spinner'
import ENDPOINTS from "../../constants/endpoints.json"
import CustomButton from '../buttons/CustomButton'
import PegboardListItem from '../../components/pegboards/PegboardListItem'
import PegboardHeader from './PegboardHeader'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import PegboardMobileListItem from './PegboardMobileListItem'
import { useRouter } from 'next/router'
import PegboardClubsListItem from './PegboardClubsListItem'
import PegboardClubMobileListItem from './PegboardClubMobileListItem'
import { ModalContext } from '../../context/ModalContext'
import constantOptions from '../../constants/constantOptions'
import { UPDATE_USER } from '../../graphql/mutations/user'
import useClient from '../../graphql/useClient'
import toastNotification from '../../utils/notifications/toastNotification'
import MESSAGES from '../../constants/messages'
import ThreeDotsIcon from '../icons/ThreeDotsIcon'
import OptionsButton from '../chat-v2/common/OptionsButton'

export default function Pegboard({ }) {
    const router = useRouter()
    const client = useClient()
    const { user, fetchUser, token } = useContext(UserContext)
    const { modal, setModal } = useContext(ModalContext)
    const [loading, setLoading] = useState(false)
    const [pegboards, setPegboards] = useState([])
    const { isMobile } = useCheckDeviceScreen()
    const [pegboardId, setPegboardId] = useState('')
    const [pegboardDetails, setPegboardDetails] = useState([])
    const [clubsList, setClubsList] = useState([])
    const [selectedPegboard, setSelectedPegboard] = useState()
    const [isCreator, setIsCreator] = useState(false)
    const [creatorMode, setCreatorMode] = useState(false)
    const [clubSearchValue, setClubSearchValue] = useState('')
    const [newClub, setNewClub] = useState({})
    const [playedClubs, setPlayedClubs] = useState([])
    const [newClubRank, setNewClubRank] = useState()
    const { USER_PEGBOARD_DETAILS_HEADER, USER_PEGBOARD_HEADER } = constantOptions
    const [editable, setEditable] = useState(0)
    const [pegboardsForDuplicate, setPegboardsForDuplicate] = useState([])
    const [openNewPegboard, setOpenNewPegboard] = useState(false)
    const [refresh, setRefresh] = useState(0)
    const [isHovered, setIsHovered] = useState(false)

    useEffect(() => {
        if (router?.query?.hidden === 'true') {
            fetchPegboards(true)
        } else {
            fetchPegboards(false)
        }
    }, [router?.query])

    useEffect(() => {
        if (selectedPegboard?.creator_id === user?.id || selectedPegboard?.creatorId === user?.id) {
            setIsCreator(true)
        } else {
            setIsCreator(false)
        }
    }, [selectedPegboard, user?.id])

    useEffect(() => {
        if (creatorMode) {
            const updatedDetails = [...pegboardDetails, { add: true }]
            setClubsList(updatedDetails)
        } else {
            setClubsList(pegboardDetails)
        }
    }, [creatorMode, pegboardDetails])

    useEffect(() => {
        if (router?.query?.type === 'pegboards' && router?.query?.id) {
            setPegboardId(router?.query?.id)
            fetchPegboardDetails(router?.query?.id)
            if (openNewPegboard?.id) {
                setSelectedPegboard(true)
                setSelectedPegboard(openNewPegboard)
                setIsCreator(true)
            }
            setOpenNewPegboard(false)
        }
    }, [router?.query])

    useEffect(() => {
        if (user) {
            fetchPegboards();
        }
    }, [refresh])

    useEffect(() => {
        if (selectedPegboard) {
            fetchPlayedClubs()
        }
    }, [selectedPegboard])

    const fetchPlayedClubs = async () => {
        try {
            await fetchUser();
            setPlayedClubs(user?.playedClubs || [])
        } catch (error) {
            console.error(error)
        }
    }

    const togglePegboardVisibility = async (pegboardId) => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS.TOGGLE_PEGBOARD_VISIBILITY, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({ pegboardId, isHidden: router?.query?.hidden === 'true' ? false : true, userId: user?.id }),
            })
                .then((data) => data.json())
                .then((data) => {
                    fetchPegboards(router?.query?.hidden === 'true' ? true : false)
                    setRefresh((prev) => prev + 1)
                    toastNotification({
                        type: constantOptions.TOAST_TYPE.SUCCESS,
                        message: router?.query?.hidden === 'true' ? 'Pegboard restored to your list' : 'Pegboard hidden successfully'
                    })
                    setLoading(false)
                    setModal()

                })
        } catch (error) {
            console.error(error)
        }
    }

    const addPegboardClub = async (clubId = newClub?.id) => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS.ADD_PEGBOARD_CLUB, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    pegboardId,
                    clubId: clubId
                }),
            })
                .then((data) => data.json())
                .then((data) => {
                    fetchPegboardDetails()
                    setClubSearchValue('')
                    setRefresh((prev) => prev + 1)
                    setNewClub({})

                })
            setLoading(false)
        } catch (error) {
            console.error(error)
        }
    }

    const fetchPegboardDetails = async (pegboardId = selectedPegboard?.id) => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS.GET_PEGBOARD_DETAILS_V3, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({ userId: user?.id, pegboardId }),
            })
                .then((data) => data.json())
                .then(({ data }) => {
                    if (!data?.pegboard) {
                        router.push({ pathname: '/dashboard/errors/404' })
                    } else {
                        setSelectedPegboard(data?.pegboard)
                        setPegboardDetails(data?.pegboardClubs);
                        setClubSearchValue('')
                    }

                })
            setLoading(false)
        } catch (error) {
            console.error(error)
        }
    }

    const handleUpdate = async (clubId) => {
        setLoading(true)
        const inPlayedClubs = playedClubs.includes(clubId)
        const updatedClubs = inPlayedClubs ? playedClubs.filter((id) => id !== clubId) : [...playedClubs, clubId]
        await client
            .request(UPDATE_USER, {
                user_id: user?.id,
                user: { playedClubs: updatedClubs },
            })
            .then(() => setPlayedClubs([...updatedClubs]))
            .catch(() => toastNotification({ type: constantOptions.TOAST_TYPE.ERROR, message: MESSAGES[500] }))
            .finally(() => setLoading(false))
    }

    const fetchPegboards = async (hidden = false) => {
        setLoading(true)
        try {
            await fetch(ENDPOINTS.GET_PEGBOARDS, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({ userId: user?.id, hidden: router?.query?.hidden !== undefined ? true : false }),
            })
                .then((data) => data.json())
                .then((data) => {
                    setPegboards(data.data);
                    setPegboardsForDuplicate(data?.data.filter((t) => [user?.id, constantOptions?.ADMIN_ID].includes(t.creatorId)));
                })
            setLoading(false)
        } catch (error) {
            console.error(error)
        }
    }

    const updateClubRank = async (clubId) => {
        if (newClubRank > pegboardDetails.length) {
            toastNotification({
                type: constantOptions.TOAST_TYPE.ERROR,
                message: MESSAGES?.PEGBOARDS?.VALID_RANK
            })
        } else {
            setLoading(true)
            await fetch(ENDPOINTS?.USER_UPDATE_PEGBOARD_CLUB_RANK, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user?.id,
                    pegboardId: pegboardId,
                    clubId,
                    newRank: newClubRank
                })
            })
                .then((response) => response.json())
                .then((data) => {
                    fetchPegboards(router?.query?.hidden === 'true' ? true : false)
                    setEditable((prev) => prev + 1)
                })
            setLoading(false)
        }
    }

    return (
        <div className="flex flex-col w-full items-center px-md md:px-lg relative">
            {loading &&
                <div className="absolute min-h-screen h-full w-full flex-center backdrop-blur-sm" style={{ zIndex: 1000 }}>
                    <ThreeDots
                        visible={true}
                        height="50"
                        width="50"
                        color={"#098089"}
                        radius="9"
                        ariaLabel="three-dots-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                    />
                </div>}
            <div className='w-full flex justify-between items-center pt-md'>
                <div className='flex items-center gap-2'>
                    {(router?.query?.hidden && router?.query?.id === undefined) && <img className='cursor-pointer' onClick={() => {
                        router.push({
                            pathname: `/dashboard/play/`,
                            search: `?type=pegboards`,
                        })
                    }} src="/svg/BackArrowBlack.svg" />}
                    <div className='text-16 lg:text-20 lg:font-medium'>{(router?.query?.hidden && router?.query?.id === undefined) ? 'Hidden Pegboards' : (router?.query?.hidden && router?.query?.id) ? 'Hidden Pegboard' : 'Pegboard'}</div>
                </div>
                {
                    <div className='flex items-center gap-2'>
                        {(selectedPegboard?.id || router?.query?.hidden) ? null :
                            <CustomButton marginX='0' width={158} height={32} text="Create New Pegboard"
                                onClick={() => setModal({
                                    type: "create-pegboard",
                                    title: "Create Pegboard",
                                    width: "829px",
                                    setRefresh,
                                    pegboards: pegboardsForDuplicate,
                                    setOpenNewPegboard: setOpenNewPegboard
                                })} />
                        }
                        {(router?.query?.hidden === undefined && router?.query?.id === undefined) &&

                            <OptionsButton
                                IconComponent={(props) => <ThreeDotsIcon {...props} />}
                                iconStyle="rounded-full"
                                top={35}
                                left={-160}
                                width={150}

                                options={{
                                    'View Hidden Pegboard': () => {
                                        router.push({
                                            pathname: `/dashboard/play/`,
                                            search: `?type=pegboards&hidden=true`,
                                        })
                                    }
                                }}
                            />
                        }
                    </div>
                }
            </div>
            <>
                {selectedPegboard ? (
                    <>
                        <div className='flex w-full items-center py-md '>
                            <div className={`flex items-center ${isMobile ? "w-full" : ""}`}>
                                <div className={`flex cursor-pointer ${isMobile ? "flex-1" : ""}`}
                                    onClick={() => {
                                        router.back()
                                        setCreatorMode(false)
                                        setPegboardId()
                                        setSelectedPegboard()
                                        setNewClub()
                                        setRefresh((prev) => prev + 1) // so that we can call the get pegboards API once again
                                    }}
                                >
                                    <img className='mr-sm' width={15} src='/svg/BackArrowBlack.svg' />
                                    <div className={`text-20 ${isMobile ? "inline" : ""}`}>{selectedPegboard?.name}</div>
                                </div>
                                {creatorMode &&
                                    <div className='h-[22px] w-[81px] flex-center rounded-lg text-10 bg-lighttealbackground2 text-darkteal ml-md'>
                                        Creator Mode
                                    </div>}

                            </div>
                            <div className='flex-1 flex justify-end'>
                                {(selectedPegboard?.creatorId || selectedPegboard?.creator_id) === user?.id &&
                                    <>
                                        {!isMobile ? (
                                            <>
                                                {(!pegboardDetails?.length) ?
                                                    <CustomButton
                                                        text={creatorMode ? 'Save Pegboard' : 'Add Clubs'}
                                                        width={isMobile ? 115 : 103}
                                                        height={32}
                                                        onClick={() => {
                                                            setCreatorMode((prev) => isCreator && !prev)
                                                        }} /> :
                                                    <CustomButton
                                                        text={creatorMode ? 'Save Pegboard' : 'Edit Clubs'}
                                                        buttonImage={creatorMode ? '' : '/svg/EditIconBlack.svg'}
                                                        color={creatorMode ? 'darkteal' : 'lightestgray'}
                                                        textColor={creatorMode ? 'white' : 'black'}
                                                        width={isMobile ? 103 : 115}
                                                        height={32}
                                                        onClick={() => {
                                                            setCreatorMode((prev) => isCreator && !prev)
                                                        }} />
                                                }
                                            </>
                                        ) : (
                                            null
                                        )}
                                    </>
                                }
                            </div>
                        </div>
                        {(selectedPegboard?.creatorId || selectedPegboard?.creator_id) === user?.id &&
                            <>
                                {isMobile ? (
                                    <div className='justify-between flex items-center w-full py-sm'>
                                        <div className='text-16'>
                                            Club{pegboardDetails?.length > 1 ? "s" : ""}({pegboardDetails?.length})
                                        </div>
                                        {(!pegboardDetails?.length) ?
                                            <>
                                                {!creatorMode ? (
                                                    <div
                                                        onClick={() => {
                                                            setCreatorMode((prev) => !prev)
                                                        }}
                                                        className='text-darkteal'>Add Clubs</div>
                                                ) : (
                                                    <CustomButton
                                                        marginX='0'
                                                        text={'Save Pegboard'}
                                                        width={115}
                                                        height={32}
                                                        onClick={() => {
                                                            setCreatorMode((prev) => !prev)
                                                        }} />
                                                )}
                                            </>
                                            :
                                            <CustomButton
                                                marginX='0'
                                                text={creatorMode ? 'Save Pegboard' : 'Edit Clubs'}
                                                buttonImage={creatorMode ? '' : '/svg/EditIconBlack.svg'}
                                                color={creatorMode ? 'darkteal' : 'lightestgray'}
                                                textColor={creatorMode ? 'white' : 'black'}
                                                width={115}
                                                height={32}
                                                onClick={() => {
                                                    setCreatorMode((prev) => isCreator && !prev)
                                                }} />
                                        }
                                    </div>
                                ) : (
                                    null
                                )}
                            </>
                        }
                        {(!pegboardDetails?.length && !creatorMode) ? (
                            <div className='flex flex-col flex-center h-[500px]'>
                                {
                                    (isCreator) ? <>
                                        <div className='text-center'>You don't have any clubs added to this Pegboard yet. Start adding clubs now</div>
                                        <di
                                            className="text-darkteal cursor-pointer"
                                            onClick={() => {
                                                setCreatorMode((prev) => isCreator && !prev)
                                            }}>+ Add club
                                        </di>
                                    </> :
                                        <div className='text-center'>There are no clubs in this Pegboard yet.</div>
                                }

                            </div>
                        ) : (
                            <>
                                {isMobile ? (<>
                                    {clubsList?.map((club) => {
                                        return (
                                            <PegboardClubMobileListItem
                                                user={user}
                                                key={club?.id}
                                                setRefresh={setRefresh}
                                                source={'pegboard'}
                                                club={club}
                                                clubs={pegboardDetails?.map((t) => t?.clubId)}
                                                pegboardId={pegboardId}
                                                isCreator={selectedPegboard?.creatorId === user?.id}
                                                fetchPegboardDetails={fetchPegboardDetails}
                                                handleUpdate={handleUpdate}
                                                creatorMode={creatorMode}
                                                updateClubRank={updateClubRank}
                                                setNewClubRank={setNewClubRank}
                                                newClubRank={newClubRank}
                                                selectedPegboard={selectedPegboard}
                                                pegboard={selectedPegboard}
                                                savePegboardClub={addPegboardClub}
                                                newClub={newClub}
                                                clubSearchValue={clubSearchValue}
                                                setClubSearchValue={setClubSearchValue}
                                                editable={editable}
                                                setCreatorMode={setCreatorMode}
                                                setPegboardId={setPegboardId}
                                                setSelectedPegboard={setSelectedPegboard}
                                                setNewClub={setNewClub}
                                                playedClubs={playedClubs || []}
                                            />
                                        )
                                    })}
                                </>) : (<>
                                    <table className="relative w-full mt-md" onDragOver={(e) => e.preventDefault()}>
                                        <PegboardHeader headers={creatorMode ? USER_PEGBOARD_DETAILS_HEADER.filter((label) => !['Friends Played', 'Mark as Played'].includes(label)) : USER_PEGBOARD_DETAILS_HEADER} />
                                        <tbody className='w-full' onDragOver={(e) => e.preventDefault()}>
                                            {clubsList?.map((club) => {
                                                return (
                                                    <PegboardClubsListItem
                                                        key={`${club?.club?.id}_${club?.rank}`}
                                                        setRefresh={setRefresh}
                                                        source={'pegboard'}
                                                        club={club}
                                                        clubs={pegboardDetails?.map((t) => t?.clubId)}
                                                        pegboardId={pegboardId}
                                                        isCreator={selectedPegboard?.creatorId === user?.id}
                                                        fetchPegboardDetails={fetchPegboardDetails}
                                                        handleUpdate={handleUpdate}
                                                        creatorMode={creatorMode}
                                                        updateClubRank={updateClubRank}
                                                        setNewClubRank={setNewClubRank}
                                                        newClubRank={newClubRank}
                                                        selectedPegboard={selectedPegboard}
                                                        playedClubs={playedClubs || []}
                                                        pegboard={selectedPegboard}
                                                        savePegboardClub={addPegboardClub}
                                                        newClub={newClub}
                                                        clubSearchValue={clubSearchValue}
                                                        setClubSearchValue={setClubSearchValue}
                                                        editable={editable}
                                                        setCreatorMode={setCreatorMode}
                                                        setPegboardId={setPegboardId}
                                                        setSelectedPegboard={setSelectedPegboard}
                                                        setNewClub={setNewClub}
                                                        clubsLength={clubsList?.length}
                                                    />)
                                            })}
                                        </tbody>
                                    </table>
                                </>)}
                            </>
                        )}
                    </>
                ) : (
                    <>{isMobile ? (
                        <div className='mt-md w-full'>
                            {pegboards?.length > 0 ? (
                                pegboards?.map((pegboard) => {
                                    return (
                                        <PegboardMobileListItem
                                            key={pegboard.id}
                                            setRefresh={setRefresh}
                                            pegboard={pegboard}
                                            pegboards={pegboards}
                                            setSelectedPegboard={setSelectedPegboard}
                                            selectedPegboard={selectedPegboard}
                                            isUser={user?.role === 'user'}
                                            userId={user?.id}
                                            togglePegboardVisibility={togglePegboardVisibility}
                                            loading={loading}
                                        />
                                    )
                                })
                            ) : (
                                <div className="flex flex-col items-center justify-center h-[400px] w-full">
                                    <img src="/svg/EmptyState.svg" alt="No pegboards" className="w-20 h-20 mb-4" />
                                    <div className="text-16 text-gray-500">
                                        {router?.query?.hidden ? "No Pegboards Hidden!" : "No pegboards available"}
                                    </div>
                                </div>
                            )}
                        </div>
                    ) : (
                        <>
                            {pegboards?.length > 0 ? (
                                <table className="relative w-full mt-md" onDragOver={(e) => e.preventDefault()}>
                                    <PegboardHeader headers={USER_PEGBOARD_HEADER} />
                                    <tbody onDragOver={(e) => e.preventDefault()}>
                                        {pegboards?.map((pegboard) => {
                                            return (
                                                <PegboardListItem
                                                    loading={loading}
                                                    togglePegboardVisibility={togglePegboardVisibility}
                                                    key={pegboard?.id}
                                                    setRefresh={setRefresh}
                                                    pegboard={pegboard}
                                                    pegboards={pegboards}
                                                    setSelectedPegboard={setSelectedPegboard}
                                                    selectedPegboard={selectedPegboard}
                                                    isUser={user?.role === 'user'}
                                                    userId={user?.id}
                                                />
                                            )
                                        })}
                                    </tbody>
                                </table>
                            ) : (
                                <div className="flex flex-col items-center justify-center h-[400px] w-full">
                                    <div className="text-16 text-gray-500">
                                        {router?.query?.hidden ? "No Pegboards Hidden!" : "No pegboards available"}
                                    </div>
                                </div>
                            )}
                        </>
                    )}
                    </>
                )}

            </>

        </div>
    )
}

