import React, { useContext, useState, useEffect, use } from 'react'
import { UserContext } from '../../pages/_app'
import { ThreeDots } from 'react-loader-spinner'
import ENDPOINTS from "../../constants/endpoints.json"
import CustomButton from '../buttons/CustomButton'
import PegboardListItem from '../../components/pegboards/PegboardListItem'
import PegboardHeader from './PegboardHeader'
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen'
import PegboardMobileListItem from './PegboardMobileListItem'
import { useRouter } from 'next/router'
import PegboardClubsListItem from './PegboardClubsListItem'
import PegboardClubMobileListItem from './PegboardClubMobileListItem'
import { ModalContext } from '../../context/ModalContext'
import constantOptions from '../../constants/constantOptions'
import { UPDATE_USER } from '../../graphql/mutations/user'
import useClient from '../../graphql/useClient'
import toastNotification from '../../utils/notifications/toastNotification'
import MESSAGES from '../../constants/messages'
import ThreeDotsIcon from '../icons/ThreeDotsIcon'
import OptionsButton from '../chat-v2/common/OptionsButton'
import PegboardList from './PegboardList'
import PegboardClubList from './PegboardClubList'

export default function Pegboard({ }) {
    const router = useRouter()
    const client = useClient()
    const { user, fetchUser, token } = useContext(UserContext)
    const { modal, setModal } = useContext(ModalContext)
    const [loading, setLoading] = useState(false)
    const [pegboards, setPegboards] = useState([])
    const { isMobile } = useCheckDeviceScreen()
    const [pegboardId, setPegboardId] = useState('')
    const [pegboardDetails, setPegboardDetails] = useState([])
    const [clubsList, setClubsList] = useState([])
    const [selectedPegboard, setSelectedPegboard] = useState()
    const [isCreator, setIsCreator] = useState(false)
    const [creatorMode, setCreatorMode] = useState(false)
    const [clubSearchValue, setClubSearchValue] = useState('')
    const [newClub, setNewClub] = useState({})
    const [playedClubs, setPlayedClubs] = useState([])
    const [newClubRank, setNewClubRank] = useState()
    const { USER_PEGBOARD_DETAILS_HEADER, USER_PEGBOARD_HEADER } = constantOptions
    const [editable, setEditable] = useState(0)
    const [pegboardsForDuplicate, setPegboardsForDuplicate] = useState([])
    const [openNewPegboard, setOpenNewPegboard] = useState(false)
    const [refresh, setRefresh] = useState(0)

    /////////////////////////////////
    const [showPegboardClubList, setShowPegboardClubList] = useState(false)

    useEffect(() => {
        if (router?.query?.id) {
            setShowPegboardClubList(true)
        } else {
            setShowPegboardClubList(false)
        }
    }, [router?.query?.id])


    return (
        <div className="min-h-screen">
            {showPegboardClubList ? (
                <PegboardClubList
                    setRefresh={setRefresh}
                />
            ) : (
                <PegboardList setModal={setModal} setRefresh={setRefresh} setOpenNewPegboard={setOpenNewPegboard} />
            )}
        </div>
    )
}

