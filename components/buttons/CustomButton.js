import React, { useEffect, useRef, useState } from 'react'
import { ThreeDots } from 'react-loader-spinner'
import useDropdown from '../../hooks/useDropdown'

/**
 * Created the button component as per the new design guide.
 */
const CustomButton = ({
    onClick = (e) => { },
    loading,
    text = '',
    width = 140,
    height = 40,
    borderRadius = 8,
    textSize = '14',
    fontWeight = 'normal',
    disabled = false,
    color = 'darkteal',
    cssClassName = '',
    marginX = 'sm',
    textColor = 'white',
    buttonImage = '',
    imageRightMargin = 11,
    darkLoader = false,
    borderColor = 'none',
    displayOptions = false,
    top = 40,
    left = 0,
    optionWidth,
    options,
    labelContainerStyle = 'border border-lightestgray',
    handleAfterAction = () => { },
    buttonImagePosition = "row",
    imageMarginBottom = "xs",
    hoverState = false,
    onMouseEnter = () => { },
    onMouseLeave = () => { },
}) => {
    const [isDisabled, setIsDisabled] = useState(disabled || false)
    const wrapperRef = useRef(null);

    const {
        dropdownVisible,
        setDropdownVisible,
        DropdownWrapper,
    } = useDropdown()

    const [dropdownPosition, setDropdownPosition] = useState({ top, left });


    useEffect(() => {
        if (loading) {
            setIsDisabled(true)
        } else {
            setIsDisabled(disabled)
        }
    }, [loading, disabled])

    useEffect(() => {
        const calculatePosition = () => {
            const dropdownElement = wrapperRef.current;
            if (dropdownElement) {
                const { bottom } = dropdownElement.getBoundingClientRect();
                const { innerHeight } = window;
                if (bottom > innerHeight) {
                    const top = dropdownElement.offsetTop - (bottom - innerHeight);
                    setDropdownPosition({ top, left });
                }
            }
        };

        if (dropdownVisible) {
            calculatePosition();
            window.addEventListener("resize", calculatePosition);
        }

        return () => {
            window.removeEventListener("resize", calculatePosition);
        };
    }, [dropdownVisible]);

    return (
        <>
            {displayOptions ? (
                <DropdownWrapper className="options--button self-center" style={{ width: width }}>
                    <button
                        id='button'
                        ref={wrapperRef}
                        onClick={() => {
                            setDropdownVisible(true)
                        }}
                        disabled={isDisabled}
                        className={`${hoverState ? "hover:bg-hover" : ""} text-${disabled ? "grayLight" : textColor} font-${fontWeight} bg-${disabled ? 'lightestgray' : color} flex-${buttonImagePosition}  rounded flex-center text-${textSize} font-regular mx-${marginX} border border-${borderColor} ${cssClassName}`}
                        style={{
                            width: width,
                            height: height,
                            borderRadius: borderRadius,
                            cursor: !disabled ? 'pointer' : 'not-allowed',
                        }}>
                        {buttonImage && <img className={`mb-${imageMarginBottom}`} style={{ marginRight: imageRightMargin }} src={`${buttonImage}`} />}
                        <div className={`flex-center`}>
                            {loading ? (
                                <ThreeDots
                                    visible={true}
                                    height="25"
                                    width="25"
                                    color={!darkLoader ? "#FFFFFF" : "#098089"}
                                    radius="9"
                                    ariaLabel="three-dots-loading"
                                    wrapperStyle={{}}
                                    wrapperClass=""
                                />
                            ) : (
                                text
                            )}
                        </div>
                        {displayOptions && <img style={{ marginLeft: "10px" }} src='/svg/WhiteArrowDown.svg' />}
                    </button>
                    {dropdownVisible && (
                        <div
                            className={`absolute bg-white flex flex-col z-50 rounded shadow black font-medium text-12 text-black`}
                            style={{ ...dropdownPosition, width: optionWidth }}
                            onClick={(e) => e.stopPropagation()}
                            ref={wrapperRef}>
                            {Object.entries(options).map(([label, action]) => (
                                <div
                                    key={label}
                                    onClick={() => {
                                        action()
                                        setDropdownVisible(false)
                                        handleAfterAction()
                                    }}
                                    className={`cursor-pointer py-sm px-md whitespace-no-wrap flex ${labelContainerStyle} `}>
                                    <div className={`cursor-pointer hover:underline z-20`}>
                                        {label}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </DropdownWrapper>
            ) : (
                <button
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    id='button'
                    ref={wrapperRef}
                    onClick={onClick}
                    disabled={isDisabled}
                    className={`
                    ${hoverState ? "hover:bg-hover" : ""} 
                    text-${disabled ? "grayLight" : textColor} 
                    font-${fontWeight} 
                    bg-${disabled ? 'lightestgray' : color} 
                    flex-${buttonImagePosition} 
                    flex-center text-${textSize} 
                    font-regular 
                    mx-${marginX} 
                    border 
                    border-${borderColor} 
                    ${cssClassName}`}
                    style={{
                        width: width,
                        height: height,
                        borderRadius: borderRadius,
                        cursor: !disabled ? 'pointer' : 'not-allowed',
                    }}>
                    {buttonImage && <img className={`mb-${imageMarginBottom}`} style={{ marginRight: imageRightMargin }} src={`${buttonImage}`} />}
                    <div className={`flex-center`}>
                        {loading ? (
                            <ThreeDots
                                visible={true}
                                height="25"
                                width="25"
                                color={!darkLoader ? "#FFFFFF" : "#098089"}
                                radius="9"
                                ariaLabel="three-dots-loading"
                                wrapperStyle={{}}
                                wrapperClass=""
                            />
                        ) : (
                            text
                        )}
                    </div>
                </button>
            )}
        </>
    )
}

export default CustomButton