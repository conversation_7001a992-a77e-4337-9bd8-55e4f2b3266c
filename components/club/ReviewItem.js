import React, { useContext } from 'react';
import moment from 'moment';
import constantOptions from '../../constants/constantOptions';
import NameInitials from '../common/NameInitials';
import { CustomStreamContext } from '../../context/CustomStreamContext';
import { DrawerContext } from '../../context/DrawerContext';
import { ModalContext } from '../../context/ModalContext';
import { UserContext } from '../../pages/_app';
import useCheckDeviceScreen from '../../hooks/useCheckDeviceScreen';

/**
 * Individual review item component for displaying a single game review
 * 
 * @param {Object} props - Component props
 * @param {Object} props.review - Review data to display
 * @param {Array} props.allFriendsId - Array of friend user IDs
 */
const ReviewItem = ({ review, allFriendsId, customBackClickHandler, imageHeight = '169px', imageWidth = 'auto', isReviewByUser = false }) => {
  const { user } = useContext(UserContext);
  const { setOtherUserId } = useContext(CustomStreamContext);
  const { drawer, setDrawer } = useContext(DrawerContext);
  const { setModal } = useContext(ModalContext);
  const { isMobile, isDesktop, isWideScreen } = useCheckDeviceScreen();

  const handleProfileClick = () => {
    if (review?.user_id === user?.id) {
      return
    }
    setOtherUserId(review?.user_id);
    setDrawer({
      type: "profile",
      source: "map",
      customBackClickHandler: customBackClickHandler,
    });
  };

  const displayName = allFriendsId.includes(review?.user_id)
    ? `${review?.first_name} ${review?.last_name}`
    : review?.username;

  return (
    <div
      key={review?.id}
      className='flex flex-col py-sm px-md border border-lightestgray border-2 rounded-lg mb-sm shadow-md'
    >
      <div className='flex items-center justify-between'>
        <div className='flex mb-sm cursor-pointer' onClick={handleProfileClick}>
          {drawer?.selectedClub ? (
            <>
              {(review?.profile_photo) ? (
                <div
                  className='h-[40px] w-[40px] rounded-full mr-sm outline outline-1.5 outline-white'
                  style={{
                    backgroundImage: `url("${review?.profile_photo}")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                ></div>
              ) : (
                <NameInitials
                  cssClassName={'mr-sm'}
                  background={'bg-lightestgray'}
                  rounded={'full'}
                  height={40}
                  width={40}
                  user={{ first_name: displayName }}
                  fontSize={16}
                />
              )}
            </>
          ) : (
            <div className=''>
              <img style={{ minWidth: '18px', minHeight: '18px' }} src='/svg/GolfPostDarkTeal.svg' />
            </div>)}

          <div className='ml-sm'>
            <div className='text-16 font-medium'>
              {isReviewByUser ? review?.club_name : displayName}
            </div>
            <div className='text-gray'>
              Game Date: {`${moment.utc(review?.game_date).format(constantOptions?.DATE_FORMAT_Do_MM_YYYY)}`}
            </div>
          </div>
        </div>
      </div>

      {(review?.photo && (isDesktop || isWideScreen)) && (
        <div
          className={`rounded-lg my-sm cursor-pointer`}
          onClick={() => {
            setModal({
              type: "image-preview",
              image: review?.photo,
              width: 600,
              height: 600
            })
          }}
          style={{
            width: isMobile ? '65px' : imageWidth,
            height: isMobile ? '50px' : imageHeight,
            backgroundImage: `url("${review?.photo}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>
      )}

      <div className='py-sm'>
        {review?.review}
      </div>

      {(review?.photo && (!isDesktop && !isWideScreen)) && (
        <div
          className={`rounded-lg my-sm cursor-pointer`}
          onClick={() => {
            setModal({
              type: "image-preview",
              image: review?.photo,
              width: 600,
              height: 600
            })
          }}
          style={{
            width: isMobile ? '65px' : imageWidth,
            height: isMobile ? '50px' : imageHeight,
            backgroundImage: `url("${review?.photo}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>
      )}
    </div>
  );
};

export default ReviewItem;