import React, { useContext, useState } from 'react';
import moment from 'moment';
import constantOptions from '../../constants/constantOptions';
import EditIcon from '../icons/EditIcon';
import DeleteIcon from '../icons/DeleteIcon';
import RequestIcon from '../icons/RequestIcon';
import { DrawerContext } from '../../context/DrawerContext';
import dateFormatter from '../../utils/helper/dateFormatter';
import { UserContext } from '../../pages/_app';
import setClevertapUser from '../../utils/clevertap/setClevertapUser';
import ENDPOINTS from '../../constants/endpoints';
import { useCurrentLocation } from '../../hooks/useCurrentLocation';
import isWithinRadius from '../../utils/helper/isWithinRadius';
import useMaintenanceStatus from '../../hooks/useMaintenanceStatus';
import toastNotification from '../../utils/notifications/toastNotification';

const { OFFER_DETAILS } = constantOptions?.DRAWER_TYPE
const { CLEVERTAP_EVENTS } = constantOptions

/**
 * Club offers section component displaying open offers for a club
 * 
 * @param {Object} props - Component props
 * @param {Array} props.offers - List of offers to display
 * @param {number} props.offersCount - Total count of offers
 * @param {Object} props.user - Current user data
 * @param {boolean} props.requestRestricted - Whether request creation is restricted
 * @param {boolean} props.showCreateRequestPopup - Whether to show request confirmation popup
 * @param {Function} props.setShowCreateRequestPopup - Function to toggle request confirmation popup
 * @param {Function} props.setModal - Function to open modal
 * @param {Function} props.onViewAllClick - Function to handle view all click
 */
const ClubOffersSection = ({
  offers = [],
  offersCount = 0,
  showCreateRequestPopup,
  setShowCreateRequestPopup,
  setModal,
  onViewAllClick,
  customBackClickHandler,
  handleOfferClick,
  setRefreshOfferDetails
}) => {
  const { drawer, setDrawer } = useContext(DrawerContext)
  const { user } = useContext(UserContext)
  const { maintenanceStatus } = useMaintenanceStatus()
  const checkToken = (offer) => {
    let body = {
      user_id: user.id,
    }
    if (offer?.creatorIsFriend) {
      body = { ...body, isRequestToAll: false }
    }
    fetch(ENDPOINTS?.CHECK_CREATE_REQUEST_TOKEN, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })
      .then((data) => data.json())
      .then((data) => {
        if (!data.canCreate) {
          setModal({
            title: 'Request Limit',
            message: data.message,
            type: 'warning',
          });
        } else {
          if (showCreateRequestPopup) {
            setModal({
              title: '',
              width: 475,
              type: 'request-confirmation-against-offer',
              offer,
              setShowCreateRequestPopup: (val) => setShowCreateRequestPopup(val)
            });
          } else {
            setModal({
              creatorIsFriend: offer?.creatorIsFriend,
              refresh: setRefreshOfferDetails,
              requestType: "OFFER",
              title: 'Request against Offer',
              img: {
                src: '/svg/request-offer.svg',
                style: {
                  height: 80,
                  marginBottom: 10,
                },
              },
              width: 475,
              type: 'request-against-offer',
              offer,
              textSize: '24px',
              additionalStyles: {
                fontWeight: 400
              }
            });
          }
        }
      })
      .catch(err => console.log('Error'));
  }

  return (
    <div className='flex flex-col px-md bg-white py-md '>
      <div className='flex justify-between'>
        <div className='text-16 mb-sm'>Open Offers ({offersCount})</div>
        {offersCount > 3 ? (
          <div
            className='text-darkteal cursor-pointer'
            onClick={onViewAllClick}
          >
            View All
          </div>
        ) : null}
      </div>
      <div className='py-sm rounded-lg justify-between'>
        {offers.length > 0 ? (
          offers.map((offer) => (
            <div
              key={offer?.id}
              className='flex flex-col py-sm pl-sm border-2 border-lightestgray rounded-lg mb-sm cursor-pointer hover:bg-tealTierBg hover:shadow-lg'
              onClick={() => {
                handleOfferClick(offer, CLEVERTAP_EVENTS?.CLICK_OFFER_IN_CLUB_DETAILS)
                setDrawer({
                  type: OFFER_DETAILS,
                  offer,
                  offerId: offer?.offer_id,
                  clubId: offer?.club_id,
                  customBackClickHandler: customBackClickHandler
                })
              }}
            >
              <div className='flex items-center justify-between'>
                <div className='flex'>
                  <div className='h-[40px] w-[40px] flex-center rounded-full bg-tealTierBg'>
                    <img width={20} src="/svg/OfferNew.svg" alt="Offer" />
                  </div>
                  <div className='ml-sm'>
                    <div className='md:text-16'>
                      Offer ID <span className='text-darkteal'>#{offer?.offer_id}</span>
                    </div>
                    <div className='text-gray'>
                      {dateFormatter(moment.utc(offer?.start_date), moment.utc(offer?.end_date))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end pr-sm">
                  {offer.user_id === user?.id ? (
                    <>
                      <div
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent drawer from opening
                          setModal({
                            title: 'Edit Offer',
                            img: {
                              src: '/svg/offer-outline.svg',
                              style: {
                                height: 48,
                                marginBottom: 10,
                              },
                            },
                            width: 829,
                            type: 'offer',
                            offer,
                            refresh: setRefreshOfferDetails,
                            source: offer?.my_tg_group_id?.length ? 'groups' : ''
                          });
                        }}
                        className="rounded-lg bg-lightestgray text-gray cursor-pointer flex-center mr-sm hover:bg-darkteal"
                        style={{ height: 30, width: 30 }}
                      >
                        <img src="/svg/EditBlack.svg" alt="Edit" />
                      </div>
                      <div
                        className='bg-lightestgray h-[30px] w-[30px] rounded-lg flex-center cursor-pointer hover:bg-red'
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent drawer from opening
                          setModal({
                            title: 'Delete Offer',
                            width: 475,
                            type: 'delete-offer',
                            offer,
                            refresh: setRefreshOfferDetails
                          });
                        }}
                      >
                        <img src="/svg/DeleteBlack.svg" alt="Delete" />
                      </div>
                    </>
                  ) : offer?.requested ? (
                    <div
                      className="px-4 py-1 rounded text-white bg-darkteal mr-sm"
                      onClick={(e) => e.stopPropagation()} // Prevent drawer from opening
                    >
                      {'Requested'}
                    </div>
                  ) : (
                    <div
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent drawer from opening
                        handleOfferClick(offer, CLEVERTAP_EVENTS?.REQUEST_AGAINST_OFFER)
                        if (maintenanceStatus?.request) {
                          toastNotification({
                            type: constantOptions.TOAST_TYPE.ERROR,
                            message: "Request flow is currently under maintenance. Please refer Request Tab for more details"
                          })
                        } else {
                          checkToken(offer)
                        }
                      }}
                      className="rounded-lg cursor-pointer flex-center mr-sm"
                      style={{ height: 30, width: 30 }}
                    >
                      <RequestIcon />
                    </div>
                  )}
                </div>
              </div>
              <div className='text-gray py-sm text-12 md:text-14'>Created By - {offer?.creatorName}</div>
              <div>{offer?.details}</div>
            </div>
          ))
        ) : (
          <div className="py-md px-md text-center text-gray">
            No offers available for this club
          </div>
        )}
      </div>
    </div>
  );
};

export default ClubOffersSection;
