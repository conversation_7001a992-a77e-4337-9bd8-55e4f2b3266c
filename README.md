# THOUSANDGREENS

## Quick Start

Clone & install dependencies

```shell script
git clone https://github.com/Launchpeer/thousandgreens-web.git
cd thousandgreens-web && npm i
```

Run the app:

```shell script
npm run dev
```

## Testing

Coming soon

## Monitoring

This application includes New Relic integration for application performance monitoring (APM). To set up monitoring:

1. Get your New Relic license key from [New Relic](https://one.newrelic.com/launcher/api-keys-ui.api-keys-launcher)
2. Set the environment variable: `NEW_RELIC_LICENSE_KEY=your_license_key`
3. Verify the integration: `npm run verify-newrelic`

For detailed setup instructions, see [New Relic Integration Guide](docs/NEW_RELIC_INTEGRATION.md).

## Documentation

Documentation is in the `/docs` folder of this project.

To display the docs in the browser:

```shell script
npm run docs
```
