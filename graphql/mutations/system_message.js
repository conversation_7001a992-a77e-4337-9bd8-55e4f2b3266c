const INSERT_SYSTEM_MESSAGE = `
mutation createSystemMessage($system_message: [system_message_insert_input!]!) {
    insert_system_message(objects: $system_message) {
      affected_rows
    }
}  
`;

const DELETE_SYSTEM_MESSAGE_FROM_WEB_PANEL = `
mutation deleteSystemMessageFromWebPanel($id: [bigint!], $userId: uuid!) {
  update_system_message(where: {id: {_in: $id}, user_id: {_eq: $userId}}, _set: {delete_from_web_panel: true, read: true}) {
      affected_rows
  }
}
`;

export { INSERT_SYSTEM_MESSAGE, DELETE_SYSTEM_MESSAGE_FROM_WEB_PANEL };
