const CREATE_NOTIFICATION = `
mutation createNotification($notifications: [notification_insert_input!]!) {
    insert_notification(objects: $notifications) {
      affected_rows
    }
}
`;

const READ_NOTIFICATION = `
mutation readNotification($id: bigint!) {
  update_notification_by_pk(pk_columns: {id: $id}, _set: {read: true}) {
    id
    read
  }
}
`;
const READ_ALL_NOTIFICATIONS = `
mutation readNotification($user_id: uuid!) {
  update_notification(where: {user_id: {_eq: $user_id}}, _set: {read: true}) {
    affected_rows
  }
}
`;

const DELETE_NOTIFICATION_FROM_WEB_PANEL = `
mutation deleteNotificationFromWebPanel($id: [bigint!], $userId: uuid!) {
  update_notification(where: {user_id: {_eq: $userId}, id: {_in: $id}}, _set: {delete_from_web_panel: true, read: true}) {
    affected_rows
  }
}
`;

export {
  CREATE_NOTIFICATION,
  READ_NOTIFICATION,
  READ_ALL_NOTIFICATIONS,
  DELETE_NOTIFICATION_FROM_WEB_PANEL,
};
