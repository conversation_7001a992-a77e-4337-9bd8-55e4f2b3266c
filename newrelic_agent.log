{"v":0,"level":30,"name":"newrelic","hostname":"<PERSON><PERSON><PERSON><PERSON><PERSON>.local","pid":22671,"time":"2025-07-02T10:16:44.835Z","msg":"Using New Relic for Node.js. Agent version: 12.23.0; Node version: v20.19.0."}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-<PERSON>pps.local","pid":22671,"time":"2025-07-02T10:16:45.022Z","msg":"new relic agent control disabled, skipping health reporting","component":"HealthReporter"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-<PERSON>pps.local","pid":22671,"time":"2025-07-02T10:16:45.056Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":30,"name":"newrelic","hostname":"<PERSON><PERSON>-<PERSON>pps.local","pid":22671,"time":"2025-07-02T10:16:45.058Z","msg":"Agent state changed from stopped to starting."}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-<PERSON>pps.local","pid":22671,"time":"2025-07-02T10:16:45.062Z","msg":"Starting New Relic for Node.js connection process."}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":22671,"time":"2025-07-02T10:16:45.062Z","msg":"Agent state changed from starting to connecting."}
