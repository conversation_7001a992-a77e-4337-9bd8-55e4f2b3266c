{"v":0,"level":30,"name":"newrelic","hostname":"<PERSON><PERSON>-<PERSON><PERSON>.local","pid":36943,"time":"2025-07-07T12:53:45.755Z","msg":"Unable to find configuration file. If a configuration file is desired (common for non-containerized environments), a base configuration file can be copied from /Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/newrelic.js and renamed to \"newrelic.js\" in the directory from which you will start your application. Attempting to start agent using environment variables."}
{"v":0,"level":30,"name":"newrelic","hostname":"<PERSON><PERSON>-<PERSON><PERSON>.local","pid":36943,"time":"2025-07-07T12:53:45.803Z","msg":"Using New Relic for Node.js. Agent version: 12.23.0; Node version: v20.19.0."}
{"v":0,"level":30,"name":"newrelic","hostname":"<PERSON><PERSON>-<PERSON><PERSON>.local","pid":36943,"time":"2025-07-07T12:53:46.228Z","msg":"new relic agent control disabled, skipping health reporting","component":"HealthReporter"}
{"v":0,"level":50,"name":"newrelic","hostname":"Sanjay-<PERSON>pps.local","pid":36943,"time":"2025-07-07T12:53:46.231Z","msg":"New Relic for Node.js was unable to bootstrap itself due to an error:","stack":"Error: New Relic requires that you name this application!\nSet app_name in your newrelic.js or newrelic.cjs file or set environment variable\nNEW_RELIC_APP_NAME. Not starting!\n    at createAgent (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:160:11)\n    at initialize (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:106:15)\n    at Object.<anonymous> (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:40:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at internalRequire (node:internal/modules/cjs/loader:216:19)\n    at Module._preloadModules (node:internal/modules/cjs/loader:1775:5)\n    at loadPreloadModules (node:internal/process/pre_execution:747:5)","message":"New Relic requires that you name this application!\nSet app_name in your newrelic.js or newrelic.cjs file or set environment variable\nNEW_RELIC_APP_NAME. Not starting!"}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":37141,"time":"2025-07-07T12:53:50.135Z","msg":"Unable to find configuration file. If a configuration file is desired (common for non-containerized environments), a base configuration file can be copied from /Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/newrelic.js and renamed to \"newrelic.js\" in the directory from which you will start your application. Attempting to start agent using environment variables."}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":37141,"time":"2025-07-07T12:53:50.162Z","msg":"Using New Relic for Node.js. Agent version: 12.23.0; Node version: v20.19.0."}
{"v":0,"level":30,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":37141,"time":"2025-07-07T12:53:50.272Z","msg":"new relic agent control disabled, skipping health reporting","component":"HealthReporter"}
{"v":0,"level":50,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":37141,"time":"2025-07-07T12:53:50.274Z","msg":"New Relic for Node.js was unable to bootstrap itself due to an error:","stack":"Error: New Relic requires that you name this application!\nSet app_name in your newrelic.js or newrelic.cjs file or set environment variable\nNEW_RELIC_APP_NAME. Not starting!\n    at createAgent (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:160:11)\n    at initialize (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:106:15)\n    at Object.<anonymous> (/Users/<USER>/Documents/projects/thousandgreens-web/node_modules/newrelic/index.js:40:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at internalRequire (node:internal/modules/cjs/loader:216:19)\n    at Module._preloadModules (node:internal/modules/cjs/loader:1775:5)\n    at loadPreloadModules (node:internal/process/pre_execution:747:5)","message":"New Relic requires that you name this application!\nSet app_name in your newrelic.js or newrelic.cjs file or set environment variable\nNEW_RELIC_APP_NAME. Not starting!"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":49910,"time":"2025-07-08T05:10:59.377Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":49912,"time":"2025-07-08T05:10:59.762Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":49912,"time":"2025-07-08T05:11:01.230Z","msg":"Next.js middleware instrumentation only supported on >=12.2.0 <=13.4.12, got 14.2.23","component":"WebFrameworkShim","module":"next/dist/server/next-server","framework":"Nextjs"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50225,"time":"2025-07-08T05:13:21.354Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50240,"time":"2025-07-08T05:13:21.949Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50241,"time":"2025-07-08T05:13:22.237Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50241,"time":"2025-07-08T05:13:23.296Z","msg":"Next.js middleware instrumentation only supported on >=12.2.0 <=13.4.12, got 14.2.23","component":"WebFrameworkShim","module":"next/dist/server/next-server","framework":"Nextjs"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50518,"time":"2025-07-08T05:14:59.965Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50533,"time":"2025-07-08T05:15:00.608Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50534,"time":"2025-07-08T05:15:01.038Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50534,"time":"2025-07-08T05:15:02.331Z","msg":"Next.js middleware instrumentation only supported on >=12.2.0 <=13.4.12, got 14.2.23","component":"WebFrameworkShim","module":"next/dist/server/next-server","framework":"Nextjs"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50825,"time":"2025-07-08T05:18:08.651Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50840,"time":"2025-07-08T05:18:09.398Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50841,"time":"2025-07-08T05:18:09.685Z","msg":"`opentelemetry_bridge` is not enabled, skipping setup of opentelemetry-bridge","component":"opentelemetry-bridge"}
{"v":0,"level":40,"name":"newrelic","hostname":"Sanjay-Dianapps.local","pid":50841,"time":"2025-07-08T05:18:10.698Z","msg":"Next.js middleware instrumentation only supported on >=12.2.0 <=13.4.12, got 14.2.23","component":"WebFrameworkShim","module":"next/dist/server/next-server","framework":"Nextjs"}
