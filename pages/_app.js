import { createContext, useEffect, useState } from 'react'
import 'firebase/compat/auth'
import initFirebase from '../utils/auth/initFirebase'
import RegisterPageWrapper from '../components/register/wrappers/RegisterPageWrapper'
import '../assets/styles/styles.css'
import 'stream-chat-react/dist/css/v2/index.css';
import 'react-phone-input-2/lib/style.css'
import '../assets/styles/transitions.css'
import '../assets/styles/stream-chat.css'
import '../assets/styles/miscellaneous.css'
import '../assets/styles/chatLayout.css'
import '../assets/styles/hamburgers.min.css'
import 'react-date-range/dist/styles.css'
import 'react-date-range/dist/theme/default.css'
import 'react-quill/dist/quill.snow.css'
import '../assets/styles/sendbird.css'
import { useRouter } from 'next/router'
import useAuth from '../utils/auth/useAuth'
import UserLayout from '../components/layout/UserLayout'
import ProfileLayout from '../components/layout/ProfileLayout'
import AdminLayout from '../components/layout/AdminLayout'
import 'core-js/stable'
import 'regenerator-runtime/runtime'
import StoreProvider from '../context/StoreContext'
import "react-datepicker/dist/react-datepicker.css";
import MobileScreenModal from '../components/modals/MobileScreenModal'
import UserSocialInfoModal from '../components/modals/UserSocialInfoModal'
import ROUTES from '../constants/routes'
import CustomStreamProvider from '../context/CustomStreamContext'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import Head from 'next/head'
import AccountMutePromptModal from '../components/modals/AccountMutePromptModal'
import OpenRequestModal from '../components/modals/OpenRequestModal'
import moment from 'moment'
import ENDPOINTS from '../constants/endpoints.json';
import initClevertap from '../utils/clevertap/initClevertap'
import SaveUserAddressModal from '../components/modals/SaveUserAddressModal'
import VisibilitySettingsModal from '../components/modals/VisibilitySettingsModal'

initFirebase()

const __DEV__ = process.env.NODE_ENV === 'development'
const apiEndpoint = process.env.CONFIG.apiEndPoint
const GiphyContext = createContext({});
export { GiphyContext }
const UserContext = createContext()
export { UserContext }
export { __DEV__, apiEndpoint }

function App({ Component, pageProps }) {
    const router = useRouter()
    const [showMutedPrompt, setShowMutedPrompt] = useState(false)
    const [showOpenRequestPrompt, setShowOpenRequestPrompt] = useState(false)
    const [showVisibilityPopup, setShowVisibilityPopup] = useState(false)
    const [saveAddressPrompt, setSaveAddressPrompt] = useState(false)
    const [isRedirectedFromPayment, setIsRedirectedFromPayment] = useState(false)
    const [isRedirectedFromDeeplink, setIsRedirectedFromDeeplink] = useState(false)
    const [clevertap, setClevertap] = useState(null);
    const [clubForRequest, setClubForRequest] = useState()
    const { user, setUser, allowAccess, fetchUser, token, membershipData, streamChatClient } = useAuth()

    useEffect(() => {
        initClevertap(clevertap, setClevertap, user);
    }, [user]);

    useEffect(() => {
        let urlHistory = sessionStorage.getItem('urlHistory') ? JSON.parse(sessionStorage.getItem('urlHistory')) : {};
        urlHistory['previous'] = urlHistory['current']
        urlHistory['current'] = router.asPath;
        sessionStorage.setItem('urlHistory', JSON.stringify(urlHistory))

        if (['/membership/customer', '/membership/payment'].includes(urlHistory['current']) || urlHistory['current'].includes('/membership/processing')) {
            setIsRedirectedFromPayment(true)
        }
    }, [router.asPath])

    useEffect(() => {
        let isMounted = true;
        if (isMounted) {
            if (user?.muted && (user?.last_muted_prompt === null || moment().diff(moment(user?.last_muted_prompt), 'hours') >= 0)) {
                setShowMutedPrompt(true);
            } else if (user?.role === 'user') {
                checkOpenRequestPopup()
            } else {
                setShowMutedPrompt(false);
            }
            isMounted = false;
        }
        return () => isMounted = false;
    }, [user?.muted, user?.last_muted_prompt, user?.show_visibility_popup])

    useEffect(() => {
        if (user?.show_visibility_popup) {
            setShowVisibilityPopup((user?.clubs?.map((data) => data?.visibleInNetwork).includes(false)));
        }
    }, [user?.show_visibility_popup])

    //Function for checking open request popup status
    async function checkOpenRequestPopup() {
        try {
            await fetch(ENDPOINTS.CHECK_OPEN_REQUEST_POPUP, {
                method: 'POST',
                headers: {
                    ['Content-Type']: 'application/json',
                    ['Authorization']: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    userId: user.id
                }),
            })
                .then((data) => data.json())
                .then((response) => {
                    if (response?.status) {
                        setShowOpenRequestPrompt(response?.data)
                    }
                })
                .catch((error) => console.log(error))

        } catch (e) {
            setShowOpenRequestPrompt(false);
        }
    }

    function renderApp() {
        if (user && allowAccess) {
            if (
                router.pathname.includes('register') ||
                router.pathname.includes('complete-profile') ||
                (router.pathname.includes('/membership') && !router.pathname.includes('/membership-logs') && !router.pathname.includes('/profile/membership'))
            ) {
                return (
                    <UserContext.Provider value={{ user, setUser, fetchUser, token, membershipData, clevertap }}>
                        <RegisterPageWrapper route={router.route} user={user} token={token} membershipData={membershipData}>
                            <Component {...pageProps} key={router.route} />
                        </RegisterPageWrapper>
                    </UserContext.Provider>
                )
            } else if (router.pathname.includes('dashboard') || router.pathname.includes('/membership-logs')) {
                return (
                    <UserContext.Provider
                        value={{ user, setUser, fetchUser, token, membershipData, streamChatClient, clevertap, clubForRequest, setClubForRequest }}>
                        <CustomStreamProvider>
                            <UserLayout user={user} token={token} membershipData={membershipData} streamChatClient={streamChatClient} setIsRedirectedFromDeeplink={setIsRedirectedFromDeeplink} clubForRequest={clubForRequest} setClubForRequest={setClubForRequest}>
                                <Component {...pageProps} key={router.route} />
                            </UserLayout>
                        </CustomStreamProvider>
                    </UserContext.Provider>
                )
            }
            else if (router.pathname.includes('admin')) {
                return (
                    <UserContext.Provider value={{ user, setUser, token, streamChatClient }}>
                        <CustomStreamProvider>
                            <AdminLayout user={user} token={token}>
                                <Component {...pageProps} key={router.route} />
                            </AdminLayout>
                        </CustomStreamProvider>
                    </UserContext.Provider>
                )
            } else if (router.pathname.includes('/profile/')) {
                return (
                    <UserContext.Provider value={{ user, setUser, fetchUser, token, streamChatClient, clevertap }}>
                        <CustomStreamProvider>
                            <ProfileLayout user={user} token={token}>
                                <Component {...pageProps} key={router.route} />
                            </ProfileLayout>
                        </CustomStreamProvider>
                    </UserContext.Provider>
                )
            } else if (router.pathname.includes('onboarding')) {
                return (
                    <UserContext.Provider value={{ user, setUser, fetchUser, clevertap }}>
                        <Component {...pageProps} key={router.route} />
                    </UserContext.Provider>
                )
            } else {
                return (
                    <Component {...pageProps} key={router.route} />
                )
            }
        } else {
            if (ROUTES.PUBLIC.includes(router.pathname)) {
                return <Component {...pageProps} key={router.route} />
            } else {
                return null;
            }
        }
    }

    return (
        <StoreProvider>
            <Head>
                <title>Thousand Greens - Golf Anywhere, Friends Everywhere</title>
            </Head>
            {
                ((router.pathname.includes('dashboard') || router.pathname.includes('/profile/')) &&
                    (user && user?.role === 'user' && (!user?.facebook && !user?.linkedin && !user?.about_yourself))) ?
                    <UserSocialInfoModal user={user} fetchUser={fetchUser} />
                    :
                    <>
                        {
                            (router.pathname === '/') ||
                                (router.pathname.includes('/login')) ||
                                (router.pathname.includes('/terms') ||
                                    router.pathname.includes('/privacy') ||
                                    router.pathname.includes('/copyright') ||
                                    router.pathname.includes('/register') ||
                                    router.pathname.includes('/terms') ||
                                    router.pathname.includes('/membership') ||
                                    router.pathname.includes('/complete-profile') ||
                                    router.pathname.includes('/pricing/newuser') ||
                                    router.pathname.includes('/pricing/renewal') ||
                                    router.pathname.includes('/legacy-pricing')
                                ) ?
                                "" :

                                user?.role === 'user' ?
                                    <div className="z-50 md:invisible">
                                        <MobileScreenModal user={user} fetchUser={fetchUser} />
                                    </div>
                                    : ''
                        }

                        {renderApp()}
                    </>
            }
            {(user?.id && (user?.is_tg_ambassador === false) && !showMutedPrompt && (showOpenRequestPrompt && (!['/membership/customer', '/membership/payment', '/membership/yearly-validation', '/membership/club-validation'].includes(router.pathname)) && !isRedirectedFromDeeplink && user?.role !== 'admin')) ? (
                <OpenRequestModal user={user} fetchUser={fetchUser} modal={true} token={token} setShowOpenRequestPrompt={setShowOpenRequestPrompt} />)
                : null}

            {(!showMutedPrompt && user?.is_tg_ambassador && !['/membership/welcome-founder'].includes(router.pathname) && (user?.stripe_customer_info?.address?.country === '' ||
                user?.stripe_customer_info?.address?.postal_code === '' || user?.stripe_customer_info === null) && !['/membership/customer', '/membership/payment', '/membership/yearly-validation', '/membership/club-validation'].includes(router.pathname) && !isRedirectedFromDeeplink && user?.role !== 'admin') ? (
                <UserContext.Provider value={{ user, setUser, fetchUser }}>
                    <SaveUserAddressModal user={user} fetchUser={fetchUser} modal={true} token={token} setSaveAddressPrompt={setSaveAddressPrompt} />
                </UserContext.Provider>
            ) : null}
            {showVisibilityPopup && (
                <>
                    {((user?.show_visibility_popup_time === null || new Date(user?.show_visibility_popup_time) <= new Date()) && !showMutedPrompt && user?.role !== 'admin' && !['/register', '/register/registration-complete', '/membership/customer', '/membership/payment', '/membership/yearly-validation', '/membership/club-validation', '/membership/processing', '/membership/welcome-back', '/membership/welcome-founder', '/membership/club-replacement-complete', '/dashboard/delete-account', '/terms', '/privacy', '/copyright', '/pricing/newuser', '/pricing/renewal', '/login'].includes(router.pathname)) ? (
                        <UserContext.Provider value={{ user, setUser, fetchUser, token }}>
                            <VisibilitySettingsModal user={user} fetchUser={fetchUser} modal={true} setShowVisibilityPopup={setShowVisibilityPopup} />
                        </UserContext.Provider>
                    ) : null}
                </>
            )}
            {(user?.id && (user?.is_tg_ambassador == false) && (showMutedPrompt && !['/complete-profile/get-started', '/membership/customer', '/membership/payment', '/membership/yearly-validation', '/membership/club-validation'].includes(router.pathname) && !isRedirectedFromPayment && !isRedirectedFromDeeplink && user?.role !== 'admin')) ? (
                <AccountMutePromptModal user={user} fetchUser={fetchUser} modal={true} setShowMutedPrompt={setShowMutedPrompt} />
            ) : null}
            <ToastContainer />
        </StoreProvider>

    )

}

export default App