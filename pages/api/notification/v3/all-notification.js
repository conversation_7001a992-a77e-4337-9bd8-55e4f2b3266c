import Joi from 'joi';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import MESSAGES from '../../../../constants/messages';
import validateUser from '../../../../utils/auth/validateUser';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import rollbar from '../../../../utils/rollbar';
import constantOptions from '../../../../constants/constantOptions';

const { NOTIFICATION_LIMIT } = constantOptions;

// Define priority notification types
const PRIORITY_NOTIFICATION_TYPES = constantOptions.PRIORITY_NOTIFICATION_TYPES;

const schema = Joi.object().keys({
  userId: Joi.string().required(),
  lastCreatedAt: Joi.string().optional().allow('', null),
  type: Joi.string().optional(),
  limit: Joi.number().optional(),
  isRead: Joi.boolean().optional(),
});

/**
 * Updated on [7th March 2025]: Fetch priority notifications first (chronologically),
 * then fetch regular notifications using pagination.
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
const allNotifications = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const {
      userId,
      lastCreatedAt = null,
      type = 'next',
      limit = NOTIFICATION_LIMIT,
      isRead,
    } = validate.value;

    const isValidUser = await validateUser(userId);
    if (!isValidUser) {
      return res.status(400).send('User does not exist.');
    }

    // Fetch priority notifications with pagination if lastCreatedAt is provided
    const priorityNotifications = await fetchPriorityNotifications(
      userId,
      isRead,
      lastCreatedAt,
      type,
    );
    // Adjust the limit for regular notifications
    const regularLimit = Math.max(limit - priorityNotifications.length, 0);

    // Fetch regular notifications only if there's space left in the limit
    const regularNotifications =
      regularLimit > 0
        ? await fetchRegularNotifications(
            userId,
            lastCreatedAt,
            type,
            regularLimit,
            isRead,
          )
        : [];

    // Combine lists, ensuring priority notifications come first
    const sortedNotifications = [
      ...priorityNotifications,
      ...regularNotifications,
    ];

    res.send(sortedNotifications);
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

/**
 * Fetch priority notifications for a user with pagination
 * @param {string} userId - The user ID
 * @param {boolean} isRead - Whether to fetch read or unread notifications
 * @param {string|null} lastCreatedAt - The timestamp for pagination
 * @param {string} type - The pagination type ('next' or 'prev')
 * @param {number} limit - The maximum number of notifications to fetch
 * @returns {Promise<Array>} - Array of priority notifications
 */
async function fetchPriorityNotifications(
  userId,
  isRead = false,
  lastCreatedAt,
  type,
) {
  const priorityTypesString = PRIORITY_NOTIFICATION_TYPES.map(
    (type) => `'${type}'`,
  ).join(', ');

  let readValue =
    isRead !== undefined ? `AND notification.read = ${isRead}` : '';

  let whereClauseNotification = `WHERE notification.user_id = $1 AND notification.mobile_panel = true ${readValue}
    AND notification.type IN (${priorityTypesString})`;

  let whereClauseSystemMessage = `WHERE system_message.user_id = $1
    AND system_message.type IN (${priorityTypesString})`;

  // Add pagination conditions if lastCreatedAt is provided
  if (lastCreatedAt) {
    const operator = type === 'next' ? '<' : '>';
    whereClauseNotification += ` AND notification.created_at ${operator} $3`;
    whereClauseSystemMessage += ` AND system_message.created_at ${operator} $3`;
  }

  const query = `
  SELECT * FROM (
    SELECT
      notification.id,
      notification.user_id,
      notification.created_at,
      notification.type,
      notification.message,
      notification.data,
      notification.read,
      notification.html_message
    FROM notification
    ${whereClauseNotification}
    
    UNION ALL

    SELECT
      system_message.id,
      system_message.user_id,
      system_message.created_at,
      system_message.type,
      system_message.message,
      system_message.data,
      true AS read,
      system_message.html_message
    FROM system_message
    ${whereClauseSystemMessage}
  ) AS priority_notifications
  ORDER BY created_at DESC;
  `;

  const params = lastCreatedAt
    ? [userId, isRead, lastCreatedAt]
    : [userId, isRead];

  return pgPoolQuery(query, params);
}

/**
 * Fetch regular (non-priority) notifications for a user with pagination
 * @param {string} user_id - The user ID
 * @param {string|null} last_created_at - The timestamp for pagination
 * @param {string} type - The pagination type ('next' or 'prev')
 * @param {number} limit - The maximum number of notifications to fetch
 * @returns {Promise<Array>} - Array of regular notifications
 */
async function fetchRegularNotifications(
  userId,
  lastCreatedAt,
  type,
  limit,
  isRead = false,
) {
  if (limit <= 0) return [];

  const priorityTypesString = PRIORITY_NOTIFICATION_TYPES.map(
    (type) => `'${type}'`,
  ).join(', ');

  let readValue =
    isRead !== undefined
      ? `AND notification.read = ${lastCreatedAt ? '$4' : '$3'}`
      : '';

  let WHERE_QUERY = `WHERE notification.user_id = $1 AND notification.mobile_panel = true ${readValue} 
    AND notification.type NOT IN (${priorityTypesString})`;

  if (lastCreatedAt) {
    if (type === 'next') {
      WHERE_QUERY += ` AND notification.created_at < $2`;
    } else {
      WHERE_QUERY += ` AND notification.created_at > $2`;
    }
  }

  const query = `
  SELECT * FROM (
    SELECT
      notification.id,
      notification.user_id,
      notification.created_at,
      notification.type,
      notification.message,
      notification.data,
      notification.read,
      notification.html_message
    FROM notification
    ${WHERE_QUERY}

    UNION ALL

    SELECT
      system_message.id,
      system_message.user_id,
      system_message.created_at,
      system_message.type,
      system_message.message,
      system_message.data,
      true AS read,
      system_message.html_message
    FROM system_message
    WHERE system_message.user_id = $1
    AND system_message.type NOT IN (${priorityTypesString})
    ${
      lastCreatedAt
        ? `AND system_message.created_at ${type === 'next' ? '<' : '>'} $2`
        : ''
    }
  ) AS regular_notifications
  ORDER BY created_at DESC
  LIMIT $${lastCreatedAt ? '3' : '2'};
  `;

  return pgPoolQuery(
    query,
    lastCreatedAt
      ? [userId, lastCreatedAt, limit, isRead]
      : [userId, limit, isRead],
  );
}

export default userGuardCheck(allNotifications);
