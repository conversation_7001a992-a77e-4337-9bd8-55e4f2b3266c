import Joi from 'joi';
import ME<PERSON>AGES from '../../../constants/messages';
import rollbar from '../../../utils/rollbar';
import userGuardCheck from '../../../utils/auth/userGuardCheck';
import { DELETE_NOTIFICATION_FROM_WEB_PANEL } from '../../../graphql/mutations/notification';
import { DELETE_SYSTEM_MESSAGE_FROM_WEB_PANEL } from '../../../graphql/mutations/system_message';
import adminClient from '../../../graphql/adminClient';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  notificationIds: Joi.array().required().items(Joi.string().trim()),
});

/**
 * API to delete a notification or system message from the web panel
 * @param {*} req
 * @param {*} res
 * @returns
 */
const DeleteNotificationFromWebPanel = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, notificationIds } = validate.value;

    let [result1, result2] = await Promise.all([
      adminClient.request(DELETE_SYSTEM_MESSAGE_FROM_WEB_PANEL, {
        id: notificationIds,
        userId: userId,
      }),
      adminClient.request(DELETE_NOTIFICATION_FROM_WEB_PANEL, {
        id: notificationIds,
        userId: userId,
      }),
    ]);

    return res.send({
      message: MESSAGES[200],
      status: 1,
      data: { notification: result2, systemMessage: result1 },
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(DeleteNotificationFromWebPanel);
