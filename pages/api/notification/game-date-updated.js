import adminClient from '../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification';
import createDynamicLink from '../../../utils/notifications/createDynamicLink';
import { GET_USER_EMAIL } from '../../../graphql/queries/user';
import { GET_GAME, GET_REQUEST_CHAT } from '../../../graphql/queries/game';
import EMAILS from '../../../constants/emails';
import rollbar from '../../../utils/rollbar';
import constantOptions from '../../../constants/constantOptions';
import pgPoolQuery from '../../../utils/db/pgQuery';
import updateTealDotFlags from '../../../utils/user/updateTealDotFlags';
import moment from 'moment';
import sendRequestSystemMessage from '../../../utils/requests/sendReqeuestSystemMessage';
/**
 * This API is triggered by game_update_notification event upon game date changed by host.
 * @param {*} req
 * @param {*} res
 */

const gameEditNotification = async (req, res) => {
  try {
    const {
      event: {
        data: {
          old: { status: oldStatus },
          new: { game_id, request_id, host_id, status: newStatus },
        },
      },
    } = req.body;

    //Fetching game detail for passing user id in GET_USER_EMAIL
    const { game_by_pk: game } = await adminClient.request(GET_GAME, {
      game_id: game_id,
    });

    // Compare old status with new status
    if (
      oldStatus !== newStatus &&
      newStatus === constantOptions.REQUEST_TAB_FILTER_OPTIONS.DECLINED
    ) {
      // Check if the user has hosted less than 5 games
      const gameCount = await pgPoolQuery(
        `
                                SELECT
                                    COUNT(g.game_id)::INT
                                FROM
                                    game g
                                JOIN request r ON r.game_id = g.game_id
                                JOIN "user" u ON u.id = host_id
                                WHERE 
                                    (g."hostCompleted" = TRUE OR g."requestorCompleted") = TRUE AND 
                                    r.status <> 'cancelled' AND 
                                    u.deleted_at IS NULL AND
                                    u.account_activated = TRUE AND
                                    g.status != 'declined' AND jsonb_array_length(r.hosts_declined) != jsonb_array_length(r.hosts_sent)
                                    AND u.id = $1
                                GROUP BY host_id
                            `,
        [host_id],
      );

      if (
        gameCount &&
        gameCount.length &&
        gameCount[0].count &&
        gameCount[0].count < constantOptions.COMPLETED_GAME_COUNT
      ) {
        await pgPoolQuery(
          ` UPDATE "user"
                    SET 
                        is_super_host = false
                    WHERE 
                        id = $1
                        RETURNING *;
                `,
          [host_id],
        );
      }

      // Check if the user has hosted 5 or more games
      const requesterGameCount = await pgPoolQuery(
        `
                                SELECT 
                                COUNT(g.game_id)::INT
                                FROM 
                                    game g
                                JOIN 
                                    request r ON r.game_id = g.game_id
                                JOIN 
                                    "user" u ON u.id = r.user_id
                                WHERE 
                                    (g."hostCompleted" = TRUE OR g."requestorCompleted" = TRUE) AND 
                                    r.status <> 'cancelled' AND 
                                    u.deleted_at IS NULL AND
                                    u.account_activated = TRUE AND
                                    g.status != 'declined' AND 
                                    jsonb_array_length(r.hosts_declined) != jsonb_array_length(r.hosts_sent) AND
                                    u.id = $1
                                GROUP BY 
                                    u.id
                                `,
        [game?.request?.user?.id],
      );

      if (requesterGameCount[0]?.count < constantOptions.COMPLETED_GAME_COUNT) {
        await pgPoolQuery(
          ` UPDATE "user"
                    SET 
                        is_super_guest = false
                    WHERE 
                        id = $1
                        RETURNING *;
                `,
          [game?.request?.user?.id],
        );
      }
    }
    const { user_by_pk: data } = await adminClient.request(GET_USER_EMAIL, {
      id: game?.request?.user?.id,
    });

    const { request_chat: requestChat } = await adminClient.request(
      GET_REQUEST_CHAT,
      {
        requestId: request_id,
        clubMemberId: host_id,
      },
    );

    // Creating short link for this request to send in email
    const { shortLink } = await createDynamicLink({
      webLink: `dashboard/play?type=requests&tab=requested&subtype=accepted`,
      appParams: `type=requests&id=${request_id}`,
    });
    const shortLinkMessage = shortLink
      ? `Click here to check details: ${shortLink}`
      : '';

    await updateTealDotFlags({
      flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
      userIds: [game?.request?.user?.id],
    });

    if (requestChat?.[0]?.stream_channel_id) {
      sendRequestSystemMessage({
        stream_channel_id: requestChat?.[0]?.stream_channel_id,
        systemChatMessage: `The game date for this game have been changed by the Host to ${moment(game?.date).format('MM/DD/YYYY')}.`,
      });
    }

    let response = await adminClient.request(CREATE_NOTIFICATION, {
      notifications: [
        {
          user_id: game?.request?.user?.id,
          type: EMAILS?.GAME_DATE_UPDATED?.type,
          message: EMAILS?.GAME_DATE_UPDATED?.message(game_id),
          text_message: EMAILS?.GAME_DATE_UPDATED?.text_message(
            game_id,
            shortLink,
          ),
          email_template_name: EMAILS?.GAME_DATE_UPDATED?.email_template_name,
          data: {
            request_id: request_id,
            template_content: [
              {
                name: 'first_name',
                content: data?.first_name,
              },
              {
                name: 'game_id',
                content: game_id,
              },
              {
                name: 'dynamic_link',
                content: shortLinkMessage,
              },
            ],
            email: data?.email,
            subject: EMAILS?.GAME_DATE_UPDATED?.subject(game_id),
          },
        },
      ],
    });
    res.status(200).send({
      status: 1,
      response,
    });
  } catch (error) {
    rollbar.error(error, req);
    res.status(500).send({
      error,
      status: 0,
    });
  }
};

export default gameEditNotification;
