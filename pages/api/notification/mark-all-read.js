import Jo<PERSON> from 'joi';
import MESSAGES from '../../../constants/messages';
import rollbar from '../../../utils/rollbar';
import pgPoolQuery from '../../../utils/db/pgQuery';
import userGuardCheck from '../../../utils/auth/userGuardCheck';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  notificationId: Joi.string().trim().optional(),
  isMobile: Joi.boolean().optional().default(false),
});

/**
 * API to mark as read all the notifications or a single notification
 * @param {*} req
 * @param {*} res
 * @returns
 */
const MarkAllNotificationAsRead = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, notificationId, isMobile = false } = validate.value;

    let query,
      params,
      notificationExist = true;

    if (notificationId) {
      const isNotification = await pgPoolQuery(
        `
        SELECT id
        FROM notification
        WHERE user_id = $1 AND id = $2
        `,
        [userId, notificationId],
      );

      if (!isNotification.length) {
        notificationExist = false;
      }
    }

    if (notificationId) {
      if (notificationExist) {
        // Mark a single notification as read
        query = `
                UPDATE notification
                SET read = true
                WHERE user_id = $1 AND id = $2
                RETURNING id
            `;
      } else {
        query = `
                UPDATE system_message
                SET read = true
                WHERE user_id = $1 AND id = $2
                RETURNING id
            `;
      }
      params = [userId, notificationId];
    } else {
      // Mark all notifications as read
      query = `
                BEGIN;
                UPDATE notification
                SET read = true
                WHERE user_id = $1 AND read = false
                RETURNING id;

                UPDATE system_message
                SET read = true
                WHERE user_id = $1 AND read = false
                RETURNING id;
                COMMIT;
            `;

      // If mobile, only mark notifications for mobile panel as read
      if (isMobile) {
        query = `
                BEGIN;
                UPDATE notification
                SET read = true
                WHERE user_id = $1 AND read = false AND mobile_panel = true
                RETURNING id;

                UPDATE system_message
                SET read = true
                WHERE user_id = $1 AND read = false
                RETURNING id;
                COMMIT;
            `;
      }
      params = [userId];
    }

    const result = await pgPoolQuery(query, params);

    return res.send({
      message: MESSAGES[200],
      status: 1,
      data: {
        notificationIds: result?.map((row) => row.id) || [],
      },
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(MarkAllNotificationAsRead);
