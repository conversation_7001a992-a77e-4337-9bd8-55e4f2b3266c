import adminClient from '../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../graphql/mutations/notification';
import checkRequestDeletedByUser from '../../../utils/requests/checkRequestDeletedByUser';
import checkMutedUserRequestInHistory from '../../../utils/requests/checkMutedUserRequestInHistory';
import createDynamicLink from '../../../utils/notifications/createDynamicLink';
import rollbar from '../../../utils/rollbar';
import MESSAGES from '../../../constants/messages';
import streamOptions from '../../../constants/streamOptions';
import getStreamInstance from '../../../utils/chat-v2/getStreamInstance';
import sendRequestSystemMessage from '../../../utils/requests/sendReqeuestSystemMessage';

export default async function NewChatNotification(req, res) {
  try {
    const { host_id, request, user, channelURL } = req.body;

    if (!host_id) {
      rollbar.info('Notification not sent: Host id is required.', req.body);

      return res.send({
        state: 0,
        message: 'Host id is required.',
      });
    }
    if (!user?.id) {
      rollbar.info('Notification not sent: User id is required.', req.body);

      return res.send({
        state: 0,
        message: 'User Id is required.',
      });
    }

    let notifications = {};
    let message = `You have a new message related to request #${request?.game_id}.`;
    let type = 'request-chat-new-message';

    //Check if the chat is initiated by the host and this is the first message
    if (channelURL) {
      const streamClient = getStreamInstance();
      const channel = streamClient.channel(
        streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP,
        channelURL,
      );

      const response = await channel.query({ messages: { limit: 10 } });

      const messages = response.messages || [];
      if (messages?.length === 1) {
        const {
          user_by_pk: { first_name, last_name },
        } = await adminClient.request(`
            {
                user_by_pk(id: "${host_id}") {
                last_name
                first_name
                }
            }
        `);
        type = 'request-chat';
        message = `${first_name} ${last_name} has initiated a chat on your Play Request #${request?.game_id}. Please respond in the chat so the host can accept your request.`;

        sendRequestSystemMessage({
          stream_channel_id: channelURL,
          systemChatMessage:
            'Chat initiated by Host for this play request. Requester must respond to confirm logistics and enable the Host to accept the request.',
        });
      } else {
        if (request.user.id === user.id) {
          const response = await streamClient.search(
            {
              type: streamOptions.CHANNEL.TYPES.REQUEST_CHAT_GROUP,
              id: channelURL,
            },
            {
              'user.id': { $eq: request.user.id },
            },
            {
              limit: 20,
              sort: [{ created_at: -1 }],
            },
          );

          if (response?.results?.length === 1) {
            await sendRequestSystemMessage({
              stream_channel_id: channelURL,
              systemChatMessage:
                'This is request is now enabled for acceptance if the logistics have been confirmed.',
            });
          }
        }
      }
    }

    if (user.id === request.user.id) {
      let webLink = `dashboard/request-chat/${request?.id}`;
      if (channelURL?.includes('sendbird')) {
        webLink = `dashboard/request/${request?.id}`;
      }
      const { shortLink } = await createDynamicLink({
        webLink: webLink,
        appParams: `type=request-chat&id=${request?.id}&channelURL=${channelURL}`,
      });
      const shortLinkMessage = shortLink
        ? `Click here to check details: ${shortLink}`
        : '';

      const requestDeleted = await checkRequestDeletedByUser({
        user_id: host_id,
        game_id: request.game_id,
      });
      const clubMutedAndInHistory = await checkMutedUserRequestInHistory({
        game_id: request.game_id,
        user_id: host_id,
      });
      if (!requestDeleted && !clubMutedAndInHistory) {
        notifications = {
          user_id: host_id,
          type,
          message,
          text_message: `${message}. ${shortLinkMessage}`,
          data: {
            request_id: request?.id,
            channelURL,
            gameId: request?.game_id,
          },
        };
      } else {
        rollbar.info(
          'Notification not sent: Request was either deleted by the user or the club is muted and in history.',
          {
            user_id: host_id,
            request_id: request?.id,
          },
        );
      }
    } else {
      let webLink = `dashboard/request-chat/${request?.id}`;
      if (channelURL?.includes('sendbird')) {
        webLink = `dashboard/request/${request?.id}`;
      }

      const { shortLink } = await createDynamicLink({
        webLink: webLink,
        appParams: `type=request-chat&id=${request?.id}&host_id=${user?.id}&channelURL=${channelURL}`,
      });
      const shortLinkMessage = shortLink
        ? `Click here to check details: ${shortLink}`
        : '';

      const requestDeleted = await checkRequestDeletedByUser({
        user_id: request.user.id,
        game_id: request.game_id,
      });
      if (!requestDeleted) {
        notifications = {
          user_id: request.user.id,
          type,
          message,
          text_message: `${message} ${shortLinkMessage}`,
          data: {
            request_id: request?.id,
            host_id: user?.id,
            channelURL,
            gameId: request?.game_id,
          },
        };
      } else {
        rollbar.info(
          'Notification not sent: Request was deleted by the user.',
          {
            user_id: request.user.id,
            request_id: request?.id,
          },
        );
      }
    }

    if (notifications?.user_id) {
      adminClient.request(CREATE_NOTIFICATION, {
        notifications: [notifications],
      });
    }

    res.send(notifications);
  } catch (error) {
    console.log('error :', error);
    rollbar.error(error, req);
    res.status(500).send({
      status: 0,
      message: MESSAGES[500],
    });
  }
}
