import MESSAGES from '../../../../constants/messages';
import <PERSON><PERSON> from 'joi';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import constantOptions from '../../../../constants/constantOptions';
import adminClient from '../../../../graphql/adminClient';
import { CREATE_NOTIFICATION } from '../../../../graphql/mutations/notification';
import EMAILS from '../../../../constants/emails';
import rollbar from '../../../../utils/rollbar';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  requestId: Joi.number().required(),
  reason: Joi.string().optional().max(300),
});

const declineRequest = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, requestId, reason } = validate.value;
    const {
      MY_TG_GROUPS_ROLES: { ADMIN, CREATOR },
    } = constantOptions;

    // Get request details
    const requestDetails = await pgPoolQuery(
      `
            SELECT 
                ccr.sender_id AS "senderId", 
                ccm.user_id AS "groupAdminId",
                cc.name AS "groupName"
            FROM chat_channel_request ccr
            LEFT JOIN 
                chat_channel_member ccm ON ccm.chat_channel_id = ccr.group_id
                    AND ccm.user_id = $2 
                    AND ccm.role IN (${ADMIN}, ${CREATOR})
            LEFT JOIN chat_channel cc ON ccr.group_id = cc.id
            WHERE ccr.id = $1
        `,
      [requestId, userId],
    );

    if (!requestDetails?.length) {
      return res.status(404).send({
        message: MESSAGES[404],
        status: 0,
      });
    }

    //Check if the user is a group admin/creator or not
    if (requestDetails[0].groupAdminId !== userId) {
      return res.status(403).send({
        message: MESSAGES.CHAT.YOU_ARE_NOT_ALLOWED,
        status: 0,
      });
    }

    //If reach here, then delete the join request
    await pgPoolQuery(
      `
                DELETE FROM chat_channel_request
                WHERE id = $1 
            `,
      [requestId],
    );

    await adminClient.request(CREATE_NOTIFICATION, {
      notifications: [
        {
          user_id: requestDetails?.[0]?.senderId,
          type: 'my-tg-groups',
          message: EMAILS.CHAT.MY_TG_GROUPS.DECLINE_INVITATION.PUSH(
            requestDetails?.[0]?.groupName,
            reason,
          ),
          data: {
            sender_id: requestDetails?.[0]?.senderId,
          },
        },
      ],
    });

    return res.send({
      message: MESSAGES[200],
      status: 1,
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(declineRequest);
