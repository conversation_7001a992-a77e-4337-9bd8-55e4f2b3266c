import pgPoolQuery from '../../../../utils/db/pgQuery';
import MESSAGES from '../../../../constants/messages';
import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import <PERSON><PERSON> from 'joi';
import Moment from 'moment';
import adminClient from '../../../../graphql/adminClient';
import sendRequestSystemMessage from '../../../../utils/requests/sendReqeuestSystemMessage';
import updateTealDotFlags from '../../../../utils/user/updateTealDotFlags';
import constantOptions from '../../../../constants/constantOptions';

const GET_REQUEST_CHAT = `
query getRequestChat($request_id: uuid!, $host_id: uuid!) {
  request_chat(where: {request_id: {_eq: $request_id}, club_member_id: {_eq: $host_id}}) {
    stream_channel_id
    club_member_id
  }
}
`;

const schema = Joi.object().keys({
  hostId: Joi.string().trim().required(),
  gameDate: Joi.string().trim().required(),
  gameId: Joi.number().integer(),
  requestId: Joi.string().trim().required(),
});

/**
 * API to get received request data with pagination.
 * @param {*} req
 * @param {*} res
 * @returns
 */

const completeGame = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { hostId, gameDate, requestId, gameId } = validate.value;

    const gameExist = await pgPoolQuery(
      `
            SELECT * FROM game
            WHERE request_id = $1
        `,
      [requestId],
    );
    if (!gameExist?.length) {
      const createGame = await pgPoolQuery(
        `
                    INSERT INTO  game (host_id, date, request_id, game_id)
                    VALUES ($1, $2, $3, $4)
                    RETURNING *
                    `,
        [hostId, Moment(gameDate).format('YYYY-MM-DD'), requestId, gameId],
      );
      const createAcceptedRequest = await pgPoolQuery(
        `
                        INSERT INTO  accepted_request (host_id, request_id, date)
                        VALUES ($1, $2, $3)
                        RETURNING *
                        `,
        [hostId, requestId, Moment(gameDate).format('YYYY-MM-DD')],
      );
    }

    const fetchHostsDeclined = await pgPoolQuery(
      `
                SELECT hosts_declined FROM request
                WHERE game_id = $1
                    `,
      [gameId],
    );

    const hostsDeclined = fetchHostsDeclined[0].hosts_declined;

    const updatedHosts = hostsDeclined.filter((host) => host !== hostId);

    const updateRequest = await pgPoolQuery(
      `
            UPDATE request
            SET hosts_declined = ${
              updatedHosts.length
                ? `'${JSON.stringify(updatedHosts)}'::jsonb`
                : 'json_build_array()'
            }, 
            cancelled_by = NULL, status = 'created'
            WHERE game_id = $2
            RETURNING *
                `,
      [updatedHosts, gameId],
    );

    const updateGame = await pgPoolQuery(
      `
            UPDATE game
            SET date = $1, declined_by_admin = FALSE, "hostCompleted" = TRUE, "requestorCompleted" = TRUE, status = 'pending', host_id = $3
            WHERE game_id = $2
            RETURNING *
                `,
      [gameDate, gameId, hostId],
    );

    const { request_chat } = await adminClient.request(GET_REQUEST_CHAT, {
      request_id: requestId,
      host_id: hostId,
    });

    if (!request_chat?.[0]?.stream_channel_id) {
    }

    sendRequestSystemMessage({
      stream_channel_id: request_chat?.[0]?.stream_channel_id,
      systemChatMessage: 'Admin has marked this game as completed.',
    });

    updateTealDotFlags({
      flagKey: constantOptions.TEAL_DOT_FLAG_KEYS.REQUEST,
      userIds: [hostId, updateRequest?.[0]?.user_id],
    });

    return res.send({
      message: MESSAGES[200],
      data: gameExist,
      status: 1,
    });
  } catch (error) {
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(completeGame);
