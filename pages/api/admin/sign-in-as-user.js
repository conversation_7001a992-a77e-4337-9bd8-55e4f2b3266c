import Jo<PERSON> from 'joi';
import MESSAGES from '../../../constants/messages';
import rollbar from '../../../utils/rollbar';
import adminGuard from '../../../utils/auth/adminGuard';
import pgPoolQuery from '../../../utils/db/pgQuery';
import { createCustomToken } from '../../../utils/auth/firebaseAdmin';

/**
 * Schema for validating the request
 */
const schema = Joi.object().keys({
  email: Joi.string().email().required(),
});

/**
 * API to send custom notifications to all users
 * @param {*} req - Request object containing message and notification types
 * @param {*} res - Response object
 */
async function signInAsUser(req, res) {
  try {
    // Validate request body
    const validate = schema.validate(req.body);

    if (validate.error) {
      const error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { email } = validate.value;

    //Fetch user using email
    const [user] = await pgPoolQuery(
      `
            SELECT id
            FROM "user"
            WHERE email = $1
            AND deleted_at IS NULL
            AND account_activated = true
            AND deactivated = false
            AND membership_active = true
        `,
      [email],
    );

    if (!user) {
      return res.status(400).send({
        status: 0,
        message: 'User not found or account is not active',
      });
    }

    const customToken = await createCustomToken(user.id);

    return res.status(200).send({
      status: 1,
      customToken,
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      status: 0,
      message: `${MESSAGES['500']}: ${error.message}`,
      error: error.stack,
    });
  }
}

export default adminGuard(signInAsUser);
