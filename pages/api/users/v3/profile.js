import userGuardCheck from '../../../../utils/auth/userGuardCheck';
import MESSAGES from '../../../../constants/messages';
import Joi from 'joi';
import constantOptions from '../../../../constants/constantOptions';
import pgPoolQuery from '../../../../utils/db/pgQuery';
import getMyTGVisibleClubs from '../../../../utils/clubs/helper/getMyTGVisibileClubs';
import rollbar from '../../../../utils/rollbar';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  golferId: Joi.string().trim().required(),
});

/**
 * API to get the enhanced v3 profile information of the golfer
 * @param {*} req
 * @param {*} res
 * @returns
 */
const getUserProfileV3 = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, golferId } = validate.value;

    let [blockedData, golferInfo, commonGroups, gameReviews, gamesPlayed] =
      await Promise.all([
        getBlockedUserData(userId, golferId),
        getGolferInfo(userId, golferId),
        getCommonGroups(userId, golferId),
        getGameReviewsGiven(golferId),
        getGamesPlayedCount(golferId),
      ]);

    if (!golferInfo || !golferInfo?.length) {
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
      });
    }

    // Get up to 5 clubs for v3
    const allClubs = await getMyTGVisibleClubs(userId, golferId);
    golferInfo[0].userClubs = allClubs.slice(0, 5);

    // Add enhanced profile data
    golferInfo[0].commonGroups = commonGroups;
    golferInfo[0].gameReviews = gameReviews?.gameReviewsData || [];
    golferInfo[0].gameReviewsCount = gameReviews?.gameReviewsCount || 0;

    // Handle enhanced friend information for friends with active requests between them
    const showEnhancedFriendInfo =
      golferInfo[0].isFriends || golferInfo[0].hasActiveRequestsBetween;

    if (showEnhancedFriendInfo) {
      // Set display name to full name for friends
      golferInfo[0].name = golferInfo[0].fullName;
      delete golferInfo[0]?.fullName;
      golferInfo[0].gamesPlayed = gamesPlayed;
    } else {
      golferInfo[0].name = golferInfo[0].username;
      delete golferInfo[0]?.phone;
      delete golferInfo[0]?.email;
      delete golferInfo[0]?.fullName;
    }

    //If the logged-in user has been blocked by the golfer then, restrict some profile information
    if (blockedData?.isBlocked && !blockedData?.blockStatus?.youHaveBlocked) {
      delete golferInfo[0]?.age;
      delete golferInfo[0]?.golfIndex;
      delete golferInfo[0]?.facebook;
      delete golferInfo[0]?.linkedin;
      delete golferInfo[0]?.private_network_id;
      delete golferInfo[0]?.userClubs;
      delete golferInfo[0]?.email;
      delete golferInfo[0]?.phone;
      delete golferInfo[0]?.formattedPhone;
      delete golferInfo[0]?.commonGroups;
      delete golferInfo[0]?.gameReviews;
      delete golferInfo[0]?.gamesPlayed;
    }

    if (golferInfo && golferInfo.length) {
      golferInfo[0].isBlocked = blockedData?.isBlocked;
      golferInfo[0].blockStatus = blockedData?.blockStatus;
    }

    return res.send({ message: MESSAGES[200], status: 1, golferInfo });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default getUserProfileV3;

/**
 * Function to get information whether the user have blocked each other or not on chat.
 * @param {*} userId
 * @param {*} golferId
 * @returns
 */
const getBlockedUserData = (userId, golferId) => {
  return new Promise(async (resolve, reject) => {
    let isBlocked = false,
      blockStatus = {
        youHaveBlocked: false,
        streamChannelId: '',
      };
    try {
      const blockedUserData = await pgPoolQuery(
        `
            SELECT
                id,
                blocked_by_id,
                blocked_userid,
                stream_channel_id
            FROM blocked_user
            WHERE
            (
                (blocked_by_id = $1 AND blocked_userid = $2)
                    OR
                (blocked_by_id = $2 AND blocked_userid = $1)
            )
          `,
        [userId, golferId],
      );

      if (blockedUserData && blockedUserData.length) {
        isBlocked = true;
        blockedUserData.map((elm) => {
          if (elm.blocked_by_id === userId) {
            blockStatus = {
              youHaveBlocked: true,
              streamChannelId: elm.stream_channel_id,
            };
          }
        });
      }
      resolve({ isBlocked, blockStatus });
    } catch (error) {
      reject(error.message);
    }
  });
};

/**
 * Function to get enhanced profile information of the golfer for v3
 * @param {*} userId
 * @param {*} golferId
 * @returns
 */
const getGolferInfo = (userId, golferId) => {
  return new Promise(async (resolve, reject) => {
    try {
      const golferData = await pgPoolQuery(
        `SELECT
                    u.id,
                    u.username AS "username",
                    user_full_name(u) AS "fullName",
                    u."profilePhoto",
                    u.tier,
                    u.created_at AS "memberSince",
                    u.about_yourself,
                    user_age(u) AS "age",
                    u.handicap AS "golfIndex",
                    u.facebook,
                    u.is_tg_founder AS "is_tg_founder",
                    u.linkedin,
                    u.muted,
                    u.is_tg_ambassador AS "is_tg_ambassador",
                    u.tg_ambassador_visibility,
                    u.is_super_host AS "isSuperHost",
                    u.email,
                    u.phone,
                    CASE WHEN f.id IS NULL THEN false
                    ELSE true END AS "isFriends",
                    CASE WHEN fr.id IS NULL THEN false
                        WHEN fr.sender_id = $1 THEN true
                    ELSE false END AS "isFriendRequestSent",
                    CASE WHEN fr.id IS NULL THEN false
                        WHEN fr.receiver_id = $1 THEN true
                    ELSE false END AS "isFriendRequestReceived",
                    fr.id AS "friendRequestId",
                    CASE WHEN fr.status = ${constantOptions.FRIEND_REQUEST_STATUS.DECLINED}
                        THEN true
                    ELSE false END AS "isFriendRequestDeclined",
                    -- Check if logged-in user and profile user have active requests between them
                    CASE WHEN (
                        EXISTS(
                            SELECT 1 FROM request r
                            LEFT JOIN game g ON r.id = g.request_id
                            LEFT JOIN request_chat rc ON r.id = rc.request_id
                            WHERE (
                              r.user_id = $1 -- userId is the requester
                              AND (
                                  g.host_id = $2 -- golferId is the host
                                  OR rc.club_member_id = $2 -- OR golferId is the club member who can accept
                              )
                              AND (
                                  r.status IN ('open', 'accepted', 'created')
                              )
                              AND (
                                  g.game_id IS NULL -- No game created yet
                                  OR (g."hostCompleted" = FALSE AND g."requestorCompleted" = FALSE) -- Game not completed
                              )
                            )
                            OR
                            (
                                r.user_id = $2 -- golferId is the requester
                                AND (
                                    g.host_id = $1 -- userId is the host
                                    OR rc.club_member_id = $1 -- OR userId is the club member who can accept
                                )
                                AND (
                                    r.status IN ('open', 'accepted', 'created')
                                )
                                AND (
                                    g.game_id IS NULL -- No game created yet)
                                    OR (g."hostCompleted" = FALSE AND g."requestorCompleted" = FALSE) -- Game not completed
                                )
                            )             
                        )
                    ) THEN true ELSE false END AS "hasActiveRequestsBetween"
                FROM "user" u
                    LEFT JOIN friends f ON (
                    (f.sender_id = $2
                            AND f.receiver_id = $1)
                        OR (f.receiver_id = $2
                            AND f.sender_id = $1)
                    )
                    LEFT JOIN friend_requests fr ON (
                        (fr.sender_id = $2
                                AND fr.receiver_id = $1)
                            OR (fr.receiver_id = $2
                                AND fr.sender_id = $1)
                        )
                WHERE u.id = $2 AND u.deactivated = false
                GROUP BY u.id, f.id, fr.id`,
        [userId, golferId],
      );
      resolve(golferData);
    } catch (error) {
      reject(error.message);
    }
  });
};

/**
 * Function to get common groups between two users (up to 5 for v3)
 * @param {*} userId
 * @param {*} golferId
 * @returns
 */
const getCommonGroups = (userId, golferId) => {
  return new Promise(async (resolve, reject) => {
    try {
      const commonGroupsData = await pgPoolQuery(
        `
                SELECT DISTINCT
                    cc.id,
                    cc.name,
                    cc.stream_channel_id,
                    cc.image
                FROM chat_channel cc
                JOIN chat_channel_member ccm1 ON ccm1.chat_channel_id = cc.id AND ccm1.user_id = $1
                JOIN chat_channel_member ccm2 ON ccm2.chat_channel_id = cc.id AND ccm2.user_id = $2
                WHERE cc.channel_type = '${constantOptions.STREAM_CHANNEL_TYPES.MY_TG_GROUP}'
                ORDER BY cc.name
                LIMIT 5
                `,
        [userId, golferId],
      );
      resolve(commonGroupsData || []);
    } catch (error) {
      reject(error.message);
    }
  });
};

/**
 * Function to get game reviews given by a user (up to 3 for v3)
 * @param {*} golferId
 * @returns
 */
const getGameReviewsGiven = (golferId) => {
  return new Promise(async (resolve, reject) => {
    try {
      const gameReviewsData = await pgPoolQuery(
        `
                SELECT
                    gr.game_id,
                    gr.review,
                    gr.photo,
                    gr.created_at,
                    c.name AS club_name,
                    g.date AS game_date
                FROM game_review gr
                INNER JOIN game g ON g.game_id = gr.game_id
                INNER JOIN request r ON r.game_id = g.game_id
                INNER JOIN courses c ON c.id = r.club_id
                WHERE gr.user_id = $1
                ORDER BY gr.created_at DESC
                LIMIT 4
                `,
        [golferId],
      );

      const gameReviewsCount = await pgPoolQuery(
        `
                SELECT
                    COUNT(*) AS total
                FROM game_review gr
                WHERE gr.user_id = $1
        
                `,
        [golferId],
      );
      resolve({
        gameReviewsData,
        gameReviewsCount: gameReviewsCount?.[0]?.total || 0,
      });
    } catch (error) {
      reject(error.message);
    }
  });
};

/**
 * Function to get games played count (requested + hosted completed)
 * @param {*} golferId
 * @returns
 */
const getGamesPlayedCount = (golferId) => {
  return new Promise(async (resolve, reject) => {
    try {
      const gamesPlayedData = await pgPoolQuery(
        `
                SELECT
                    COUNT(*) FILTER (WHERE g.host_id = $1) AS hosted,
                    COUNT(*) FILTER (WHERE r.user_id = $1) AS requested,
                    COUNT(*) AS total
                FROM
                    request r
                LEFT JOIN game g ON (r.game_id = g.game_id)
                WHERE
                    (r.user_id = $1 OR g.host_id = $1)
                    AND r.status != 'cancelled'
                    AND (g."hostCompleted" = TRUE OR g."requestorCompleted" = TRUE)
                    AND g.declined_by_admin = FALSE
                `,
        [golferId],
      );

      const result = gamesPlayedData?.[0] || {
        hosted: 0,
        requested: 0,
        total: 0,
      };
      resolve({
        hosted: parseInt(result.hosted) || 0,
        requested: parseInt(result.requested) || 0,
        total: parseInt(result.total) || 0,
      });
    } catch (error) {
      reject(error.message);
    }
  });
};
