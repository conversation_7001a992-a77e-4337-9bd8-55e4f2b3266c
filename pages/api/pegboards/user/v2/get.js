import Joi from 'joi';
import MESSAGES from '../../../../../constants/messages';
import rollbar from '../../../../../utils/rollbar';
import adminClient from '../../../../../graphql/adminClient';
import { USER_PLAYED_CLUBS } from '../../../../../graphql/queries/user';
import pgPoolQuery from '../../../../../utils/db/pgQuery';
import userGuardCheck from '../../../../../utils/auth/userGuardCheck';
import constantOptions from '../../../../../constants/constantOptions';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  hidden: Joi.boolean().optional().default(false),
});

/**
 * API to get user visible pegboard list
 * Filters based on user's hidden pegboards from user_informations.pegboard_hidden array
 * @param {*} req
 * @param {*} res
 * @returns
 */
const getPegboardList = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, hidden } = validate.value;

    const { user_by_pk } = await adminClient.request(USER_PLAYED_CLUBS, {
      user_id: userId,
    });

    const playedClubs = user_by_pk?.playedClubs.filter((club) => !!club);

    // Get user's hidden pegboards from user_informations table
    const userHiddenPegboards = await pgPoolQuery(
      `SELECT pegboard_hidden FROM user_informations WHERE user_id = $1`,
      [userId],
    );

    let hiddenPegboardIds = [];
    if (
      userHiddenPegboards.length > 0 &&
      userHiddenPegboards[0].pegboard_hidden
    ) {
      hiddenPegboardIds = userHiddenPegboards[0].pegboard_hidden;
    }

    // Create condition based on hidden parameter and user's hidden pegboards
    let hiddenCondition = '';
    if (hidden) {
      // Show only hidden pegboards
      if (hiddenPegboardIds.length > 0) {
        const hiddenIds = hiddenPegboardIds.map((id) => `'${id}'`).join(',');
        hiddenCondition = `pb.id IN (${hiddenIds}) AND`;
      } else {
        // No hidden pegboards, return empty result
        return res.send({
          status: 1,
          message: MESSAGES[200],
          data: [],
        });
      }
    } else {
      // Show only non-hidden pegboards
      if (hiddenPegboardIds.length > 0) {
        const hiddenIds = hiddenPegboardIds.map((id) => `'${id}'`).join(',');
        hiddenCondition = `pb.id NOT IN (${hiddenIds}) AND`;
      }
      // If no hidden pegboards, show all (no additional condition needed)
    }

    const pegboards = await pgPoolQuery(
      `
        WITH DistinctChatChannels AS (
                SELECT
                    pb.id AS pegboard_id,
                    jsonb_agg(jsonb_build_object('id', cc.id, 'name', cc.name) ORDER BY cc.id ASC) AS chat_channels
                FROM 
                    pegboard pb
                LEFT JOIN chat_channel cc ON cc.id = ANY(pb.my_tg_group_id) AND NOT (-1 = ANY(pb.my_tg_group_id))
                WHERE cc.id IS NOT NULL
                
                GROUP BY pb.id
                )
            SELECT
                pb.id,
                pb.name,
                COUNT(DISTINCT pc.club_id)::INT AS "totalClubs",
                (COUNT(DISTINCT pc.club_id) FILTER (WHERE pc.club_id = ANY($2)))::INT AS "totalPlayedClubs",
                pb.creator_id as "creatorId",
                (
                    CASE
                        WHEN (pb.creator_id = $1 OR friends.id IS NOT NULL) THEN CONCAT(u.first_name, ' ', u.last_name)
                        WHEN (pb.creator_id = '${constantOptions.ADMIN_ID}') THEN 'TG Admin'
                        ELSE u.username
                    END 
                ) AS "creatorName",
                pb.is_private AS "isPrivate",
                pb.for_friends AS "forFriends",
                pb.my_tg_group_id,
                pb.visible_to_all AS "visibleToAll",
                dcc.chat_channels AS "chatChannels",
                CASE
                    WHEN $4::uuid[] IS NOT NULL AND pb.id = ANY($4::uuid[]) THEN true
                    ELSE false
                END AS "isHidden"
            FROM 
                pegboard pb
            LEFT JOIN DistinctChatChannels dcc ON pb.id = dcc.pegboard_id
            LEFT JOIN pegboard_club pc ON pc.pegboard_id = pb.id
            LEFT JOIN "user" u ON u.id = pb.creator_id
            LEFT JOIN chat_channel_member ccm ON ccm.user_id = $1
            LEFT JOIN chat_channel_member creator_channels ON creator_channels.user_id = pb.creator_id AND creator_channels.chat_channel_id = ccm.chat_channel_id
            LEFT JOIN friends ON (
                ($1 = friends.sender_id AND pb.creator_id = friends.receiver_id)
                OR
                ($1 = friends.receiver_id AND pb.creator_id = friends.sender_id)
            )
            WHERE
                    ${hiddenCondition}
                    --pegboard created by the admin and visible to all OR visible to some TG groups or all TG groups
                    CASE 
                        WHEN pb.creator_id = $1 THEN TRUE -- if the user is the creator, then always show him the pegboard
                        WHEN 
                            pb.visible_to_all = TRUE OR 
                            (
                                ccm.chat_channel_id = ANY(pb.my_tg_group_id) OR 
                                (creator_channels.chat_channel_id IS NOT NULL AND pb.my_tg_group_id[1] = -1) OR
                                (pb.my_tg_group_id[1] = -1 AND pb.creator_id = $3 AND ccm.chat_channel_id IS NOT NULL) -- if the pegboard (by admin) was created for all groups, then members with any my tg group must see it
                            )
                        THEN TRUE
                        -- pegboard is created for private only ie visible to only creator
                        WHEN pb.is_private = TRUE AND pb.creator_id = $1 THEN TRUE
                        -- pegboard is visible to the creator friends
                        WHEN pb.for_friends = TRUE AND friends.id IS NOT NULL THEN TRUE
                        ELSE FALSE
                    END
            
            GROUP BY u.username, friends.id, pb.id, u.first_name, u.last_name, pb.my_tg_group_id, dcc.chat_channels
            ORDER BY 
                CASE 
                    WHEN pb.creator_id = $1 THEN 0
                    WHEN pb.creator_id = $3 THEN 1
                END,
                CASE 
                    WHEN pb.creator_id = $1 THEN pb.name
                END,
                CASE 
                    WHEN pb.creator_id = $3 THEN pb."order" 
                END;
            
            `,
      [
        userId,
        playedClubs,
        process.env.NEXT_PUBLIC_ADMIN_ID,
        hiddenPegboardIds,
      ],
    );

    return res.send({
      status: 1,
      message: MESSAGES[200],
      data: pegboards,
    });
  } catch (error) {
    rollbar.error(error, req);
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(getPegboardList);
