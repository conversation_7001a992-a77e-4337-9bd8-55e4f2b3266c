import Jo<PERSON> from 'joi';
import MESS<PERSON>ES from '../../../../../constants/messages';
import userGuardCheck from '../../../../../utils/auth/userGuardCheck';
import pgPoolQuery from '../../../../../utils/db/pgQuery';
import adminClient from '../../../../../graphql/adminClient';

const schema = Joi.object().keys({
  userId: Joi.string().trim().required(),
  pegboardId: Joi.string().trim().required(),
});

const FETCH_PEGBOARD_DETAILS = `
query pegboardDetails($id: uuid!) {
    pegboard_by_pk(id: $id) {
        id
        name
        my_tg_group_id
        is_private
        my_tg_group_id
        for_friends
        creator_id
    }
} 
`;

/**
 * API to get pegboard details - this is a v2 api made after api/pegboards/user/pegboard-details
 * This query is a little more optimized than the before and does not include the "isPlayed" key
 * @param {*} req
 * @param {*} res
 */

const pegboardDetails = async (req, res) => {
  try {
    // Validate request body
    let validate = schema.validate(req.body);

    if (validate.error) {
      let error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { userId, pegboardId } = validate.value;

    //Get user's hidden pegboards
    const userHiddenPegboards = await pgPoolQuery(
      `SELECT pegboard_hidden FROM user_informations WHERE user_id = $1`,
      [userId],
    );

    // Run both queries in parallel using Promise.all
    const [pegboardDetails, pegboardData] = await Promise.all([
      pgPoolQuery(
        `
                WITH "friendsData" AS (
                    SELECT
                        u.id AS "friend_id",
                        u.first_name,
                        u.last_name,
                        u."profilePhoto",
                        u."playedClubs"
                    FROM
                        friends f
                    JOIN
                        "user" u ON u.id = CASE 
                        WHEN f.sender_id = $1 THEN f.receiver_id
                        ELSE f.sender_id
                    END
                    WHERE 
                        (f.sender_id = $1 OR f.receiver_id = $1)
                        AND f.status = 1
                ),
                "playedFriendsData" AS (
                    SELECT
                        pbc.club_id,
                        fd."friend_id",
                        fd.first_name,
                        fd.last_name,
                        fd."profilePhoto",
                        ROW_NUMBER() OVER (PARTITION BY pbc.club_id) AS rn
                    FROM
                        pegboard_club pbc
                    INNER JOIN
                        "friendsData" fd ON fd."playedClubs" @> jsonb_build_array(pbc.club_id)
                    WHERE pbc.pegboard_id = $2
                ),
                "playedFriendsDataGrouped" AS (
                    SELECT
                        pfd.club_id,
                        COUNT(pfd."friend_id") AS "friendsPlayedInClubCount",
                        json_agg(
                            json_build_object(
                                'id', pfd."friend_id",
                                'firstName', pfd.first_name,
                                'lastName', pfd.last_name,
                                'profilePhoto', pfd."profilePhoto"
                            )
                        ) FILTER (WHERE rn <= 3) AS "friendsPlayedInClub"
                    FROM
                        "playedFriendsData" pfd
                    GROUP BY
                        pfd.club_id
                )

                SELECT
                    pb.id AS "id",
                    c.name AS "clubName",
                    c.id AS "clubId",
                    pfda."friendsPlayedInClubCount",
                    pfda."friendsPlayedInClub",
                    (
                        CASE
                        WHEN pb.creator_id = $1
                            THEN true
                        ELSE
                            FALSE
                        END
                    ) AS "isCreator",
                    pbc.rank::INT AS "rank"
                FROM
                    pegboard pb
                JOIN pegboard_club pbc ON pbc.pegboard_id = pb.id AND pbc.pegboard_id = $2
                JOIN
                    courses c ON c.id = pbc.club_id
                LEFT JOIN
                    "playedFriendsDataGrouped" pfda ON pfda.club_id = c.id
                WHERE 
                    pbc.pegboard_id = $2
                ORDER BY pbc.rank
                `,
        [userId, pegboardId],
      ),
      adminClient.request(FETCH_PEGBOARD_DETAILS, {
        id: pegboardId,
      }),
    ]);

    return res.send({
      status: 1,
      message: MESSAGES[200],
      data: {
        pegboardClubs: pegboardDetails,
        pegboard: {
          ...pegboardData?.pegboard_by_pk,
          isHidden:
            userHiddenPegboards?.[0]?.pegboard_hidden?.includes(pegboardId),
        },
      },
    });
  } catch (error) {
    return res.status(500).send({
      message: `${MESSAGES['500']}: ${error}`,
      status: 0,
    });
  }
};

export default userGuardCheck(pegboardDetails);
