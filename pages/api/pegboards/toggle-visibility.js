import Jo<PERSON> from 'joi';
import MESSAGES from '../../../constants/messages';
import adminClient from '../../../graphql/adminClient';
import userGuardCheck from '../../../utils/auth/userGuardCheck';
import rollbar from '../../../utils/rollbar';
import pgPoolQuery from '../../../utils/db/pgQuery';

const schema = Joi.object().keys({
  pegboardId: Joi.string().trim().required(),
  isHidden: Joi.boolean().required(),
  userId: Joi.string().trim().required(),
});

// PostgreSQL query to get current user's hidden pegboards
const GET_USER_HIDDEN_PEGBOARDS_QUERY = `
    SELECT pegboard_hidden
    FROM user_informations
    WHERE user_id = $1
`;

// PostgreSQL query to insert user_informations if not exists
const INSERT_USER_INFORMATIONS_QUERY = `
    INSERT INTO user_informations (user_id, pegboard_hidden)
    VALUES ($1, $2::uuid[])
    ON CONFLICT (user_id)
    DO UPDATE SET
        pegboard_hidden = $2::uuid[]
    RETURNING pegboard_hidden
`;

// GraphQL query to get pegboard information
const GET_PEGBOARD_INFO = `
query getPegboardInfo($pegboard_id: uuid!) {
    pegboard_by_pk(id: $pegboard_id) {
        id
        name
        creator_id
    }
}
`;

/**
 * API to hide/unhide a pegboard for a specific user
 * Uses user_informations.pegboard_hidden array to track hidden pegboards per user
 * @param {*} req
 * @param {*} res
 * @returns
 */
const togglePegboardVisibility = async (req, res) => {
  try {
    // Validate request body
    const validate = schema.validate(req.body);

    if (validate.error) {
      const error = validate.error.details[0].message;
      return res.status(400).send({
        status: 0,
        message: MESSAGES[400],
        error,
      });
    }

    const { pegboardId, isHidden, userId } = validate.value;

    // Check if pegboard exists using GraphQL
    const { pegboard_by_pk: pegboard } = await adminClient.request(
      GET_PEGBOARD_INFO,
      {
        pegboard_id: pegboardId,
      },
    );

    if (!pegboard) {
      return res.status(404).send({
        status: 0,
        message: 'Pegboard not found',
      });
    }

    // Get current user's hidden pegboards
    const userInfoResult = await pgPoolQuery(GET_USER_HIDDEN_PEGBOARDS_QUERY, [
      userId,
    ]);

    let currentHiddenPegboards = [];
    if (userInfoResult.length > 0 && userInfoResult[0].pegboard_hidden) {
      currentHiddenPegboards = userInfoResult[0].pegboard_hidden;
    }

    // Check if pegboard is already in the requested state
    const isPegboardCurrentlyHidden =
      currentHiddenPegboards.includes(pegboardId);

    if (isPegboardCurrentlyHidden === isHidden) {
      return res.status(200).send({
        status: 1,
        message: `Pegboard is already ${isHidden ? 'hidden' : 'visible'} for this user`,
        data: {
          id: pegboard.id,
          name: pegboard.name,
          isHidden: isPegboardCurrentlyHidden,
        },
      });
    }

    // Update the hidden pegboards array
    let updatedHiddenPegboards;
    if (isHidden) {
      // Add pegboard to hidden list
      updatedHiddenPegboards = [...currentHiddenPegboards, pegboardId];
    } else {
      // Remove pegboard from hidden list
      updatedHiddenPegboards = currentHiddenPegboards.filter(
        (id) => id !== pegboardId,
      );
    }

    console.log('updatedHiddenPegboards :', updatedHiddenPegboards);
    // Update or insert user_informations record
    const [updatedUserInfo] = await pgPoolQuery(
      INSERT_USER_INFORMATIONS_QUERY,
      [userId, updatedHiddenPegboards],
    );

    return res.status(200).send({
      status: 1,
      message: isHidden
        ? 'Pegboard hidden from your view'
        : 'Pegboard visible in your view',
      data: {
        id: pegboard.id,
        name: pegboard.name,
        isHidden: isHidden,
        hiddenPegboards: updatedUserInfo.pegboard_hidden,
      },
    });
  } catch (error) {
    console.log('error :', error);
    rollbar.error('Error toggling pegboard visibility', {
      error: error.message,
      pegboardId: req.body?.pegboardId,
      userId: req.body?.userId,
      isHidden: req.body?.isHidden,
    });

    return res.status(500).send({
      status: 0,
      message: `${MESSAGES['500']}: ${error.message}`,
    });
  }
};

export default userGuardCheck(togglePegboardVisibility);
