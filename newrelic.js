'use strict';
require('dotenv').config({ path: '.env.local' });

// Determine environment-specific app name
const getAppName = () => {
  const environment = process.env.ENVIRONMENT || 'development';
  const appNames = {
    development: 'Thousandgreens-dev',
    qa: 'Thousandgreens-qa',
    staging: 'Thousandgreens-staging',
    production: 'Thousandgreens-prod',
  };
  return [appNames[environment] || 'Thousandgreens-dev'];
};

exports.config = {
  app_name: getAppName(),
  license_key: process.env.NEW_RELIC_LICENSE_KEY,
  logging: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'warn',
  },
  distributed_tracing: {
    enabled: true,
  },
  error_collector: {
    enabled: true,
    ignore_status_codes: [404],
  },
  transaction_tracer: {
    enabled: true,
    transaction_threshold: 'apdex_f',
    record_sql: 'obfuscated',
    explain_threshold: 500,
  },
  slow_sql: {
    enabled: true,
  },
};
