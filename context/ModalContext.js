import React, { useState, useEffect, useContext } from 'react'
import { DrawerContext } from './DrawerContext'
import {
    ActivateUserModal,
    AddClubModal,
    AddFeedPostModal,
    AddToPersonalNetworkModal,
    AnnualConfirmationModal,
    RequestModal,
    EditClubModal,
    ClubMemberTransferModal,
    PostFormModal,
    DeletePostModal,
    DeleteCommentModal,
    DeclineUserModal,
    AssociationModal,
    DeleteAssociationModal,
    ClubActivationModal,
    PrivateNetworkModal,
    DeactivateNetworkModal,
    EditUserModal,
    ResetPasswordModal,
    DeleteRequestModal,
    DeleteRequestModalNew,
    ClubMemberEmailModal,
    PNMemberEmailModal,
    OfferModal,
    EventModal,
    DeleteEventModal,
    ForumCategoryModal,
    WarningModal,
    UpgradeSubscriptionModal,
    CancelSubscriptionModal,
    ConfirmRequestModal,
    CreateFAQModal,
    EditFAQModal,
    EditContactPersonalNetworkModal,
    DeleteContactPersonalNetworkModal,
    RequestTokenAdjModal,
    ResolveReportedPostModal,
    ReferFriendModal,
    CreateRequestConfirmationAgainstOfferModal,
    EnhanceFavoriteModal,
    SearchFavoriteClubModal,
    MyFavoriteClubModal,
    InadequateFavouriteClubModal,
    CalendarWarningModal,
    OfferCalendarWarningModal,
    NewPegboardClubModal,
    DeletePrivateNetworkUserModal,
    AddPNUserModal,
    AddMyContactModal,
    ApproveEventModal,
    TierFilterAlertModal,
    ModalHeader
} from '../components/modals'
import AddPrivateNetworkModal from '../components/modals/AddPrivateNetworkModal'
import BenefitModal from '../components/modals/BenefitModal'
import ChatGroupModal from '../components/modals/ChatGroupModal'
import DeleteBenefitModal from '../components/modals/DeleteBenefitModal'
import DeleteOfferModal from '../components/modals/DeleteOfferModal'
import DeletePegboardModal from '../components/modals/DeletePegboardModal'
import DeclineTokenAdjustmentModal from '../components/modals/DeclineTokenAdjustmentModal'
import EditUserClubsModal from '../components/modals/EditUserClubsModal'
import GolferProfileModal from '../components/modals/GolferProfileModal'
import JoinChatGroupModal from '../components/modals/JoinChatGroupModal'
import PegboardModal from '../components/modals/PegboardModal'
import PromoCodeModal from '../components/modals/PromoCodeModal'
import RequestAgainstOfferModal from '../components/modals/RequestAgainstOfferModal'
import RequestorConfirmRequestModal from '../components/modals/RequestorConfirmRequestModal'
import AdminDeleteClubModal from '../components/modals/AdminDeleteClubModal'
import { checkAnnualConfirmationStatus } from '../utils/user'
import CreateAdminModal from '../components/modals/CreateAdminModal'
import DeleteChatGroupModal from '../components/modals/DeleteChatGroupModal'
import DeleteUserModal from '../components/modals/DeleteUserModal'
import MuteUserModal from '../components/modals/MuteUserModal'
import ReviewGameModal from '../components/modals/ReviewGameModal'
import AssignTierModal from '../components/modals/AssignTierModal'
import LeavePrivateNetworkModal from '../components/modals/LeavePrivateNetworkModal'
import DeclineAcceptedRequestModal from '../components/modals/DeclineAcceptedRequestModal'
import RequesterInfoModal from '../components/modals/RequesterInfoModal'
import GameExperienceHostModal from '../components/modals/GameExperienceHostModal'
import MuteUserClubModal from '../components/modals/MuteUserClubModal'
import LeaveThousandGreens from '../components/modals/LeaveThousandGreens'
import AcceptRequestConfirmationModal from '../components/modals/AcceptRequestConfirmationModal';
import CreateRequestConfirmationModal from '../components/modals/CreateRequestConfirmationModal';
import { VerifyPhoneNumberModal } from '../components/modals'
import { VerifyEmailModal } from '../components/modals'
import { ChangePasswordModal } from '../components/modals'
import DeleteMyContactModal from '../components/modals/DeleteMyContactModal'
import DisclaimerModal from '../components/modals/DisclaimerModal'
import EditMyContactModal from '../components/modals/EditMyContactModal'
import UserLogoutModal from '../components/modals/UserLogoutModal'
import HostInfoModal from '../components/modals/HostInfoModal'
import CannotDeleteUserModal from '../components/modals/CannotDeleteUserModal'
import DeleteTGAccountModal from '../components/modals/DeleteTGAccountModal'
import DeleteTGAccountConfirmationModal from '../components/modals/DeleteTGAccountConfirmationModal'
import AdminChangePasswordModal from '../components/modals/AdminChangePasswordModal'
import AddThousandGreens from '../components/modals/AddThousandGreens'
import BenefitCategoryModal from '../components/modals/BenefitCategoryModal'
import NgvAdjModal from '../components/modals/NgvAdjModal'
import EditPaymentDetailsModal from '../components/modals/EditPaymentDetailsModal'
import MyFriendModal from '../components/modals/MyFriendModal'
import RequesterClubModal from '../components/modals/RequesterClubModal'
import ReplaceClubConfirmationModal from '../components/modals/ReplaceClubConfirmationModal'
import PaymentDetailModal from '../components/modals/PaymentDetailModal'
import RevertGameModal from '../components/modals/RevertGameModal'
import MutualFriendsModal from '../components/modals/MutualFriendsModal'
import DeclineRequestModal from '../components/modals/DeclineRequestModal'
import UnfriendModal from '../components/modals/UnfriendModal'
import FriendRequestNotesModal from '../components/modals/FriendRequestNotesModal'
import DeleteModal from '../components/modals/DeleteModal'
import ForwardMessageModal from '../components/modals/ForwardMessageModal'
import AddGroupParticipantsModal from '../components/modals/AddGroupParticipantsModal'
import ImageCropModal from '../components/modals/ImageCropModal'
import UpdateAdminOfGroupModal from '../components/modals/UpdateAdminOfGroupModal'
import BlockUnblockConfimationModal from '../components/modals/BlockUnblockConfirmationModal'
import ClubFriendsModal from '../components/modals/ClubFriendsModal'
import ClubFriendsPlayedModal from '../components/modals/ClubFriendsPlayedModal'
import MuteChatModal from '../components/modals/MuteChatModal'
import ArchiveChatModal from '../components/modals/ArchiveChatModal'
import GroupMembersModal from '../components/modals/GroupMembersModal'
import RemoveGroupParticipantModal from '../components/modals/RemoveGroupParticipantModal'
import ChannelPhotoModal from '../components/modals/ChannelPhotoModal'
import SearchAdminGroupParticipantsModal from '../components/modals/SearchAdminGroupParticipantsModal'
import BroadcastMessageModal from '../components/modals/BroadcastMessageModal'
import CreateGroupModal from '../components/modals/CreateGroupModal'
import CreateGroupSuccessModal from '../components/modals/CreateGroupSuccessModal'
import ConfirmationModal from '../components/modals/ConfirmationModal'
import DeleteGroupModal from '../components/modals/DeleteGroupModal'
import MyGroupRequestNotesModal from '../components/modals/MyGroupRequestNotesModal'
import AcceptDeclineGroupRequest from '../components/modals/AcceptDeclineGroupRequest'
import WithdrawGroupRequestModal from '../components/modals/WithdrawGroupRequestModal'
import ConfirmThresholdChangesModal from '../components/modals/ConfirmThresholdChangesModal'
import AdminMarkOverRequested from '../components/modals/AdminMarkOverRequested'
import AdminCompleteRequestModal from '../components/modals/AdminCompleteRequestModal'
import OverrideGroupSettingModal from '../components/modals/OverrideGroupSettingModal'
import MyTGCommonGroupsModal from '../components/modals/MyTGCommonGroupsModal'
import OfferDetailsModal from '../components/modals/OfferDetailsModal'
import TgGroupMembersModal from '../components/modals/TgGroupMembersModal'
import DeletePegboardConfirmationModal from '../components/modals/DeletePegboardConfirmationModal'
import DeletePegboardClubModal from '../components/modals/DeletePegboardClubModal'
import CreatePegboardModal from '../components/modals/CreatePegboardModal'
import AddNewPegboardClubModal from '../components/modals/AddNewPegboardClubModal'
import disableGlobalScroll from '../utils/helper/disableGlobalScroll'
import ImagePreviewPopup from '../components/modals/ImagePreviewPopup'
import EditClubVisibilityModal from '../components/modals/EditClubVisibilityModal'
import useModalActive from '../hooks/useModalActive'
import GroupOfferModal from '../components/modals/GroupOfferModal'
import HowItWorksModal from '../components/modals/HowItWorksModal'
import AcceptDeclineEventModal from '../components/modals/AcceptDeclineEventModal'
import RecommendationsModal from '../components/modals/RecommendationsModal'
import VisibilitySettingsModal from '../components/modals/VisibilitySettingsModal'
import EditDefaultMessageModal from '../components/modals/EditDefaultMessageModal'
import SaveUserAddressModal from '../components/modals/SaveUserAddressModal'
import PollModal from '../components/modals/PollModal'
import AddPollCommentModal from '../components/modals/AddPollCommentModal'
import EndPollModal from '../components/modals/EndPollModal'
import GameLogModal from '../components/modals/GameLogModal'
import UnmuteClubReminderModal from '../components/modals/UnmuteClubReminderModal'
import SimpleListModal from '../components/modals/SimpleListModal'
import GameRequesterInfoModal from '../components/modals/GameRequesterInfoModal'

const ModalContext = React.createContext()
export { ModalContext }

function Modal({ modal, setModal, userData }) {
    const { drawer } = useContext(DrawerContext)
    const [globalDropdownVisible, setGlobalDropdownVisible] = useState(false)
    const [transition, setTransition] = useState(false)
    const { isModalActive, setIsModalActive } = useModalActive()

    useEffect(() => {
        setTransition(true)
    }, [])

    useEffect(() => {
        const isDrawerOpen = window?.sessionStorage?.getItem("DRAWER_OPEN", true)

        if (modal) {
            disableGlobalScroll(Object?.entries(modal)?.length > 0)
        } else {
            if (!isModalActive) {
                disableGlobalScroll(false)
            }
        }
        return (() => {
            if (!isDrawerOpen) {
                disableGlobalScroll(false)
            }
        })
    }, [modal])

    const scrollToBottom = () => {
        var myDiv = document.getElementById("ModalContext");
        myDiv.scrollTop = myDiv.scrollHeight;
    }

    function renderModal() {
        switch (modal.type) {
            case 'create-admin':
                return <CreateAdminModal modal={modal} setModal={setModal} />
            case 'activate-user':
                return <ActivateUserModal modal={modal} setModal={setModal} />
            case 'add-club':
                return <AddClubModal modal={modal} setModal={setModal} />
            case 'delete-chat-group':
                return (
                    <DeleteChatGroupModal modal={modal} setModal={setModal} />
                )
            case 'delete-PN-user':
                return (
                    <DeletePrivateNetworkUserModal modal={modal} setModal={setModal} />
                )
            case 'add-PN-user':
                return (
                    <AddPNUserModal modal={modal} setModal={setModal} />
                )
            case 'add-feed-post':
                return <AddFeedPostModal modal={modal} setModal={setModal} />
            case 'annual-confirmation':
                return (
                    <AnnualConfirmationModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                    />
                )
            case 'edit-club':
                return (
                    <EditClubModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                    />
                )
            case 'leave-network':
                return (
                    <LeavePrivateNetworkModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'request':
                return (
                    <RequestModal
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        modal={modal}
                        setModal={setModal}
                        scrollToBottom={scrollToBottom}
                    />
                )
            case 'request-accept':
                return (
                    <AcceptRequestConfirmationModal
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'request-confirmation':
                return (
                    <CreateRequestConfirmationModal
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'request-confirmation-against-offer':
                return (
                    <CreateRequestConfirmationAgainstOfferModal
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'confirm-request':
                return (
                    <ConfirmRequestModal
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'confirmation-modal':
                return (
                    <ConfirmationModal modal={modal} setModal={setModal} />
                )
            case 'decline-accepted-request':
                return (
                    <DeclineAcceptedRequestModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'club-member-transfer':
                return (
                    <ClubMemberTransferModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'calendar-warning':
                return (
                    <CalendarWarningModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'offer-calendar-warning':
                return (
                    <OfferCalendarWarningModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'review-game':
                return <ReviewGameModal modal={modal} setModal={setModal} />
            case 'post-form':
                return <PostFormModal modal={modal} setModal={setModal} />
            case 'delete-post':
                return <DeletePostModal modal={modal} setModal={setModal} />
            case 'delete-comment':
                return <DeleteCommentModal modal={modal} setModal={setModal} />
            case 'delete-group':
                return <DeleteGroupModal modal={modal} setModal={setModal} />
            case 'decline-user':
                return <DeclineUserModal modal={modal} setModal={setModal} />
            case 'association':
                return <AssociationModal modal={modal} setModal={setModal} />
            case 'delete-association':
                return (
                    <DeleteAssociationModal modal={modal} setModal={setModal} />
                )
            case 'club-activation':
                return <ClubActivationModal modal={modal} setModal={setModal} />
            case 'private-network':
                return <PrivateNetworkModal modal={modal} setModal={setModal} />
            case 'deactivate-network':
                return (
                    <DeactivateNetworkModal modal={modal} setModal={setModal} />
                )
            case 'reset-password':
                return <ResetPasswordModal modal={modal} setModal={setModal} />

            case 'change-password':
                return <ChangePasswordModal modal={modal} setModal={setModal} />
            case 'admin-change-password':
                return <AdminChangePasswordModal modal={modal} setModal={setModal} />
            case 'decline-token-adjustment':
                return <DeclineTokenAdjustmentModal modal={modal} setModal={setModal} />
            case 'delete-account':
                return <DeleteTGAccountModal modal={modal} setModal={setModal} />
            case 'delete-account-confirmation':
                return <DeleteTGAccountConfirmationModal modal={modal} setModal={setModal} />
            case 'decline-friend-request':
                return <DeclineRequestModal modal={modal} setModal={setModal} />
            case 'unfriend':
                return <UnfriendModal modal={modal} setModal={setModal} />
            case 'friend-request-notes':
                return <FriendRequestNotesModal modal={modal} setModal={setModal} />
            case 'delete-pegboard-club':
                return <DeletePegboardClubModal modal={modal} setModal={setModal} />
            case 'visibility-settings':
                return <VisibilitySettingsModal modal={modal} setModal={setModal} />

            case 'edit-user':
                return (
                    <EditUserModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                    />
                )

            case 'edit-user-clubs':
                return <EditUserClubsModal modal={modal} setModal={setModal} />
            case 'edit-my-contact':
                return <EditMyContactModal modal={modal} setModal={setModal} />
            case 'admin-delete-club':
                return (<AdminDeleteClubModal modal={modal} setModal={setModal} />)
            case 'club-member-email':
                return (<ClubMemberEmailModal modal={modal} setModal={setModal} />)
            case 'pn-member-email':
                return (<PNMemberEmailModal modal={modal} setModal={setModal} />)
            case 'delete-request':
                return <DeleteRequestModal modal={modal} setModal={setModal} />
            case 'delete-request-new':
                return <DeleteRequestModalNew modal={modal} setModal={setModal} />
            case 'add-my-contact':
                return <AddMyContactModal modal={modal} setModal={setModal} />
            case 'my-friend':
                return <MyFriendModal modal={modal} setModal={setModal} />
            case 'requester-club':
                return <RequesterClubModal modal={modal} setModal={setModal} />
            case 'simple-list':
                return <SimpleListModal modal={modal} setModal={setModal} />
            case 'offer':
                return (
                    <OfferModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        scrollToBottom={scrollToBottom}
                    />
                )
            case 'group-offer':
                return (
                    <GroupOfferModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                        scrollToBottom={scrollToBottom}
                    />
                )
            case 'delete-offer':
                return <DeleteOfferModal modal={modal} setModal={setModal} />

            case 'event':
                return (
                    <EventModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                    />
                )
            case 'delete-event':
                return <DeleteEventModal modal={modal} setModal={setModal} />
            case 'delete-pegboard-confirmation':
                return <DeletePegboardConfirmationModal modal={modal} setModal={setModal} />
            case 'approve-event':
                return <ApproveEventModal modal={modal} setModal={setModal} />
            case 'tier-filter':
                return <TierFilterAlertModal modal={modal} setModal={setModal} />
            case 'delete-my-contact':
                return <DeleteMyContactModal modal={modal} setModal={setModal} />
            case 'delete-chat':
                return <DeleteModal modal={modal} setModal={setModal} />
            case 'forum-category':
                return <ForumCategoryModal modal={modal} setModal={setModal} />
            case 'benefit-category':
                return <BenefitCategoryModal modal={modal} setModal={setModal} />
            case 'disclaimer':
                return (
                    <DisclaimerModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'warning':
                return <WarningModal modal={modal} setModal={setModal} />
            case 'create-faq':
                return <CreateFAQModal modal={modal} setModal={setModal} />
            case 'create-edit-group':
                return <CreateGroupModal modal={modal} setModal={setModal} />
            case 'group-create-success':
                return <CreateGroupSuccessModal modal={modal} setModal={setModal} />
            case 'edit-faq':
                return <EditFAQModal modal={modal} setModal={setModal} />
            case 'add-to-personal-network':
                return (
                    <AddToPersonalNetworkModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'edit-contact-personal-network':
                return (
                    <EditContactPersonalNetworkModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'delete-contact-personal-network':
                return (
                    <DeleteContactPersonalNetworkModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'add-private-network':
                return (
                    <AddPrivateNetworkModal modal={modal} setModal={setModal} />
                )
            case 'requestor-confirm-request':
                return (
                    <RequestorConfirmRequestModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'golfer-profile':
                return <GolferProfileModal modal={modal} setModal={setModal} />
            case 'request-against-offer':
                return (
                    <RequestAgainstOfferModal
                        modal={modal}
                        setModal={setModal}
                        scrollToBottom={scrollToBottom}
                    />
                )
            case 'my-tg-common-groups':
                return (
                    <MyTGCommonGroupsModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'pegboard':
                return <PegboardModal modal={modal} setModal={setModal} />
            case 'create-pegboard':
                return <CreatePegboardModal modal={modal} setModal={setModal} />
            case 'new club':
                return <NewPegboardClubModal modal={modal} setModal={setModal} />
            case 'delete-pegboard':
                return <DeletePegboardModal modal={modal} setModal={setModal} />
            case 'assign-tier':
                return (
                    <AssignTierModal
                        modal={modal}
                        setModal={setModal}
                        globalDropdownState={{
                            globalDropdownVisible,
                            setGlobalDropdownVisible,
                        }}
                    />
                )
            case 'benefit':
                return <BenefitModal modal={modal} setModal={setModal} />
            case 'delete-benefit':
                return <DeleteBenefitModal modal={modal} setModal={setModal} />
            case 'resolve-reported-post':
                return (
                    <ResolveReportedPostModal
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'refer-friend':
                return <ReferFriendModal modal={modal} setModal={setModal} />
            case 'promo-code':
                return <PromoCodeModal modal={modal} setModal={setModal} />
            case 'TG group':
                return <ChatGroupModal modal={modal} setModal={setModal} />
            case 'join-chat-group':
                return <JoinChatGroupModal modal={modal} setModal={setModal} />
            case 'forward-message':
                return <ForwardMessageModal modal={modal} setModal={setModal} />
            case 'block-unblock-user':
                return <BlockUnblockConfimationModal modal={modal} setModal={setModal} />
            case 'add-group-participants':
                return <AddGroupParticipantsModal modal={modal} setModal={setModal} />
            case 'crop-image':
                return <ImageCropModal modal={modal} setModal={setModal} />
            case 'remove-group-participant':
                return <RemoveGroupParticipantModal modal={modal} setModal={setModal} />
            case 'request-token-adjust':
                return (
                    <RequestTokenAdjModal modal={modal} setModal={setModal} />
                )
            case 'update-admin-of-group':
                return <UpdateAdminOfGroupModal modal={modal} setModal={setModal} />
            case 'replace-club-confirmation':
                return (
                    <ReplaceClubConfirmationModal modal={modal} setModal={setModal} />
                )
            case 'delete-user':
                return <DeleteUserModal modal={modal} setModal={setModal} />
            case 'cannot-delete-user':
                return <CannotDeleteUserModal modal={modal} setModal={setModal} />
            case 'mute-user':
                return <MuteUserModal modal={modal} setModal={setModal} />
            case 'enhance-favorite':
                return <EnhanceFavoriteModal modal={modal} setModal={setModal} />
            case 'inadequate-favourite-clubs':
                return <InadequateFavouriteClubModal modal={modal} setModal={setModal} />
            case 'search-favorite-club':
                return <SearchFavoriteClubModal modal={modal} setModal={setModal} />
            case 'search-admin-group-participants':
                return <SearchAdminGroupParticipantsModal modal={modal} setModal={setModal} />
            case 'my-favorite-club':
                return <MyFavoriteClubModal modal={modal} setModal={setModal} />
            case 'mutual-friends':
                return <MutualFriendsModal modal={modal} setModal={setModal} />
            case 'club-friends':
                return <ClubFriendsModal modal={modal} setModal={setModal} />
            case 'club-friends-played':
                return <ClubFriendsPlayedModal modal={modal} setModal={setModal} />
            case 'requester-info':
                return <RequesterInfoModal modal={modal} setModal={setModal} />
            case 'game-experience-host':
                return <GameExperienceHostModal modal={modal} setModal={setModal} />
            case 'host-info':
                return <HostInfoModal modal={modal} setModal={setModal} />
            case 'mute-user-club':
                return <MuteUserClubModal modal={modal} setModal={setModal} />
            case 'verify-phone':
                return <VerifyPhoneNumberModal modal={modal} setModal={setModal} />
            case 'verify-email':
                return <VerifyEmailModal modal={modal} setModal={setModal} />
            case 'user-logout':
                return <UserLogoutModal modal={modal} setModal={setModal} />
            case 'ngv-adjust':
                return <NgvAdjModal modal={modal} setModal={setModal} />
            case 'edit-payment':
                return <EditPaymentDetailsModal modal={modal} setModal={setModal} />
            case 'payment-detail':
                return <PaymentDetailModal modal={modal} setModal={setModal} />
            case 'leave-thousand-greens':
                return (
                    <LeaveThousandGreens
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'add-thousand-greens':
                return (
                    <AddThousandGreens
                        modal={modal}
                        setModal={setModal}
                    />
                )
            case 'revert-game':
                return <RevertGameModal modal={modal} setModal={setModal} />
            case 'mute-chat':
                return <MuteChatModal modal={modal} setModal={setModal} />
            case 'archive-chat':
                return <ArchiveChatModal modal={modal} setModal={setModal} />
            case 'group-members':
                return <GroupMembersModal modal={modal} setModal={setModal} />
            case 'channel-photo':
                return <ChannelPhotoModal modal={modal} setModal={setModal} />
            case 'broadcast-message':
                return <BroadcastMessageModal modal={modal} setModal={setModal} />
            case 'my-group-request-notes':
                return <MyGroupRequestNotesModal modal={modal} setModal={setModal} />
            case 'accept-decline-group-request':
                return <AcceptDeclineGroupRequest modal={modal} setModal={setModal} />
            case 'withdraw-group-request':
                return <WithdrawGroupRequestModal modal={modal} setModal={setModal} />
            case 'confirm-threshold-changes':
                return <ConfirmThresholdChangesModal modal={modal} setModal={setModal} />
            case 'admin-mark-over-requested':
                return <AdminMarkOverRequested modal={modal} setModal={setModal} />
            case 'admin-complete-request':
                return <AdminCompleteRequestModal modal={modal} setModal={setModal} />
            case 'override-group-setting':
                return <OverrideGroupSettingModal modal={modal} setModal={setModal} />
            case 'offer-details':
                return <OfferDetailsModal modal={modal} setModal={setModal} />
            case 'tg-group-members':
                return <TgGroupMembersModal modal={modal} setModal={setModal} />
            case 'add-new-pegboard-club':
                return <AddNewPegboardClubModal modal={modal} setModal={setModal} />
            case 'image-preview':
                return <ImagePreviewPopup modal={modal} setModal={setModal} />
            case 'edit-club-visibility':
                return <EditClubVisibilityModal modal={modal} setModal={setModal} />
            case 'how-it-works':
                return <HowItWorksModal modal={modal} setModal={setModal} />
            case 'accept-decline-event':
                return <AcceptDeclineEventModal modal={modal} setModal={setModal} />
            case 'recommendations':
                return <RecommendationsModal modal={modal} setModal={setModal} />
            case 'edit-default-message':
                return <EditDefaultMessageModal modal={modal} setModal={setModal} />
            case 'save-user-address':
                return <SaveUserAddressModal modal={modal} setModal={setModal} />
            case 'create-poll':
                return <PollModal modal={modal} setModal={setModal} />
            case 'comment-poll':
                return <AddPollCommentModal modal={modal} setModal={setModal} />
            case 'end-poll':
                return <EndPollModal modal={modal} setModal={setModal} />
            case 'game-log':
                return <GameLogModal modal={modal} setModal={setModal} />
            case 'unmute-club-reminder':
                return <UnmuteClubReminderModal modal={modal} setModal={setModal} />
            case 'game-requester-info':
                return <GameRequesterInfoModal modal={modal} setModal={setModal} />
        }
    }

    return (
        <div
            id='ModalContext'
            onClick={() => {
                if (['group-create-success', 'admin-mark-over-requested', 'delete-account-confirmation', 'delete-user'].includes(modal?.type)) {
                    null
                } else {
                    setModal()
                }
            }}
            className="fixed inset-0 py-xl"
            style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                boxSizing: 'border-box',
                minHeight: '100vh',
                zIndex: **********,
                overflowY: 'scroll',
                opacity: transition ? 1 : 0,
                transition: 'opacity 0.2s ease-in-out',
                backdropFilter: 'blur(2px)'
            }}>
            <div
                className="flex-center w-full px-md"
                style={{ minHeight: '100%' }}>
                <div
                    onClick={(e) => {
                        e.stopPropagation()
                        setGlobalDropdownVisible(false)
                    }}
                    className={`bg-white rounded-lg shadow pt-lg ${(['other-members', 'forward-message', 'group-members'].includes(modal?.type)) ? 'pb-0' : 'pb-lg'} flex flex-col flex-center relative w-full`}
                    style={{
                        maxWidth: modal?.width || 500,
                        transform: transition
                            ? 'scale(1) translateY(0px)'
                            : 'scale(0.9) translateY(50px)',
                        transition: 'transform 0.2s ease-in-out',
                    }}>
                    {modal?.grayImgBg && (
                        <div
                            className={'bg-lightestgray absolute flex-center'}
                            style={{
                                top: 55,
                                left: 200,
                                height: 65,
                                width: 65,
                                zIndex: -1,
                                borderRadius: 50,
                            }}
                        />
                    )}
                    {modal.img && <>
                        {modal?.img?.backgroundColor ? (
                            <div className={`flex-center bg-${modal.img.backgroundColor} rounded-full h-${modal.img.backgroundHeight} w-${modal.img.backgroundWidth}`}>
                                <img src={modal.img.src} style={modal.img.style} />

                            </div>
                        ) : (
                            (
                                <img src={modal.img.src} style={modal.img.style} />
                            )
                        )}
                    </>}
                    {modal?.title ?
                        <div
                            className={`text-black text-2xl text-center capitalize ${modal.type === 'mute-user-club' ? 'px-md' : ""}`}
                            style={{ width: modal.titleWidth, fontSize: modal.textSize, ...(modal?.additionalStyles || {}) }}>
                            {modal.title}
                        </div>
                        : null}
                    {renderModal()}
                </div>
            </div>
        </div>
    )
}

export default function GlobalModal({ user, children }) {
    const [modal, setModal] = useState()

    //Date :- 26.08.2022 as we will not be showing annual confirmation feature in this membership sprint.
    // const [displayPopup, setDisplayPopup] = useState(false)
    // const [isDisplayed, setIsDisplayed] = useState(false)

    // useEffect(() => {
    //     const isDisplayed = localStorage.getItem('display-annual-confirmation-popup');
    //     if (isDisplayed) {
    //         const display = JSON.parse(isDisplayed)
    //         setIsDisplayed(display)
    //     }
    // }, [])

    // useEffect(() => {
    //     if(user){
    //         const needsConfirmation = checkAnnualConfirmationStatus(user)
    //         // const needsConfirmation = true // used for debugging
    //         setDisplayPopup(needsConfirmation)
    //     }
    // }, [user])

    // useEffect(() => {
    //     if (user) {
    //         if (displayPopup && isDisplayed) {
    //             setModal({
    //                 type: 'annual-confirmation',
    //                 modal,
    //                 setModal,
    //                 width: 800,
    //             })
    //         }
    //     }
    // }, [user, displayPopup, isDisplayed])

    return (
        <ModalContext.Provider value={{ modal, setModal }}>
            {children}
            {modal && <Modal modal={modal} setModal={setModal} />}
        </ModalContext.Provider>
    )
}
