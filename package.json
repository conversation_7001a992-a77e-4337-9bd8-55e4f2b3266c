{"name": "thousandgreens-web", "version": "1.0.0", "cacheDirectories": [".next/cache"], "engines": {"node": "<=18.20.5"}, "browser": {"fs": false, "child_process": false}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p $PORT", "docs": "docsify serve docs", "hasura-logs": "heroku logs -a thousandgreens-hasura --tail", "heroku-cleanup": "rm -rf .next/cache && find node_modules/@next -depth -maxdepth 1 -type d -name 'swc*' -exec rm -rf {} \\; -prune #Remove cache and SWC binaries to reduce heroku slug size", "web-logs": "heroku logs -a thousandgreens-web-dev --tail", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "pq": "pretty-quick", "prebuild": "next telemetry disable", "lint": "eslint . --ignore-pattern '**/__tests__/**'", "prepare": "husky"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@logtail/js": "^0.4.21", "@react-google-maps/api": "^2.19.3", "@stripe/react-stripe-js": "^1.16.5", "@stripe/stripe-js": "^1.54.2", "@wojtekmaj/react-timerange-picker": "^3.5.0", "axios": "^1.7.9", "babel-loader": "^9.2.1", "chart.js": "^2.9.4", "clevertap-web-sdk": "^1.9.3", "compressorjs": "^1.2.1", "core-js": "^3.38.1", "country-state-city": "^3.2.1", "date-fns": "^2.30.0", "emoji-mart": "^5.6.0", "eslint-config-next": "^14.2.10", "express-rate-limit": "^6.11.2", "express-slow-down": "^1.6.0", "firebase": "^10.7.1", "firebase-admin": "^12.4.0", "formik": "^2.4.6", "get-user-locale": "^1.5.1", "google-libphonenumber": "^3.2.38", "graphql-request": "^2.0.0", "ioredis": "^5.4.1", "joi": "^17.13.3", "js-cookie": "2.2.1", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "mandrill-api": "1.0.45", "mapbox-gl": "^2.15.0", "match-sorter": "^6.3.4", "md5": "^2.3.0", "micro": "^9.4.1", "moment": "^2.30.1", "multiparty": "^4.2.3", "nanoid": "^5.0.9", "newrelic": "^12.23.0", "next": "^14.2.10", "next-connect": "^0.11.1", "next-iron-session": "^4.2.0", "next-page-transitions": "^1.0.0-beta.2", "node-fetch": "^2.7.0", "pg": "^8.12.0", "pg-promise": "^10.15.4", "postcss-import": "^16.1.0", "prop-types": "15.7.2", "rate-limit-redis": "^3.1.0", "react": "^18.3.1", "react-csv": "^2.2.2", "react-date-range": "^1.4.0", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-google-places-autocomplete": "^4.1.0", "react-google-recaptcha": "^2.1.0", "react-icons": "^4.12.0", "react-image-crop": "^10.1.8", "react-infinite-scroll-component": "^5.1.0", "react-lazy-load-image-component": "^1.6.2", "react-loader-spinner": "^6.1.6", "react-multi-select-component": "^4.3.4", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-toastify": "^9.1.3", "react-transition-group": "^4.4.5", "regenerator-runtime": "^0.13.11", "rollbar": "^2.26.4", "slick-carousel": "^1.8.1", "stream-chat": "^8.49.0", "stream-chat-react": "^12.8.1", "stripe": "^9.16.0", "swiper": "^11.1.15", "swr": "2.2.5", "twilio": "^5.3.0", "xhr2": "^0.2.1", "yup": "^0.29.3"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-runtime": "^7.25.4", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/runtime": "^7.25.6", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "autoprefixer": "^10.4.20", "babel-core": "^4.7.16", "babel-jest": "^29.7.0", "babel-preset-env": "^0.0.0", "babel-preset-react": "^6.24.1", "docsify-cli": "^4.4.1", "enzyme": "^3.11.0", "enzyme-to-json": "^3.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.35.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "postcss": "^8.4.45", "prettier": "^3.5.3", "pretty-quick": "^2.0.1", "tailwindcss": "^3.4.12"}}