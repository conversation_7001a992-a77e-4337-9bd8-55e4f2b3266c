import moment from "moment"
import constantOptions from "./constantOptions"

const streamOptions = {
    USER_STATUS: {
        DELETED: 'Deleted User'
    },
    CHANNEL: {
        TYPES: {
            ONE_TO_ONE: "messaging",
            SYST<PERSON>: "system_channel",
            USER_CREATED_GROUP: "user_created_group",
            SYSTEM_THOUSAND_GREENS_PUBLIC: "system_thousand_greens_public",
            SYSTEM_PRIVATE_NETWORK: "system_private_network",
            ADMIN_CREATED_GROUP: 'admin_created_group',
            MY_TG_GROUP: 'my_tg_group',
            REQUEST_CHAT_GROUP: 'request_chat_group'
        },
        STANDARD_FILTERS: (userId, time = moment(), hidden = false, chatType = 'all') => {
            const baseFilter = {
                members: { $in: [userId] },
                hidden: { $eq: hidden },
            };

            const groupTypes = [
                streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.SYSTEM_PRIVATE_NETWORK,
                streamOptions.CHANNEL.TYPES.SYSTEM_THOUSAND_GREENS_PUBLIC,
                streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
                streamOptions.CHANNEL.TYPES.MY_TG_GROUP
            ];

            switch (chatType) {
                case 'dm':
                    return {
                        ...baseFilter,
                        type: { $eq: streamOptions.CHANNEL.TYPES.ONE_TO_ONE },
                        last_message_at: { $lte: moment(time).add(2, 'hours') }
                    };
                case 'group':
                    return {
                        ...baseFilter,
                        type: { $in: groupTypes }
                    };
                case 'all':
                default:
                    return {
                        ...baseFilter,
                        $or: [
                            {
                                $and: [
                                    {
                                        last_message_at: { $lte: moment(time).add(2, 'hours') }
                                    },
                                    {
                                        type: { $eq: streamOptions.CHANNEL.TYPES.ONE_TO_ONE }
                                    }
                                ]
                            },
                            {
                                $and: [
                                    {
                                        type: {
                                            $in: groupTypes
                                        }
                                    }
                                ]
                            }
                        ]
                    };
            }
        },
        SORT: { last_message_at: -1 },
        OPTIONS: { limit: 30, message_limit: constantOptions?.STREAM_MESSAGE_LIMIT },
    },
    MAX_FILES_ALLOWED_TO_UPLOAD: 10,
    AVATAR_CHANNEL_NAME_HEIGHT: 55,
    DELETED_MESSAGE_TEXT: "This message was deleted.",
    ACCEPTED_FORMATS: {
        PHOTO_VIDEO: ["image/heif", "image/heic", "image/png", "image/jpeg", "image/png", "image/gif", "video/mp4", "video/mpeg", "video/webm", "video/x-matroska", "video/quicktime"],
        DOCUMENTS: ["image/heif", "image/heic", "text/csv", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/html", "audio/mpeg",
            "application/pdf", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "text/plain", "audio/wav",
            "audio/webm", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/x-iwork-numbers-sffnumbers",
            "application/vnd.apple.numbers", "application/x-msexcel", "application/x-excel", "application/zip"]
    },
    MAX_IMAGE_SIZE: {
        SIZE_IN_MB: 1e+7, // 10 Mb
        ERROR: (noOfFiles) => {
            if (noOfFiles === 1) {
                return `1 of the images could not be uploaded as it was greater than 10 Mb`
            } else if (noOfFiles > 1) {
                return `${noOfFiles} of the images could not be uploaded as they were greater than 10 Mb`
            }
        }
    },
    CHANNEL_QUERY_LIMIT: 30,
    STREAM_QUERY_OPTIONS: {
        FORWARD_FILTER: (userId, time, searchString = '') => {
            let searchStringExists = (searchString.trim() !== 0) && searchString?.length

            if (searchStringExists) {
                return {
                    members: { $in: [userId] },
                    $or: [
                        {
                            $and: [
                                {
                                    type: { $eq: streamOptions.CHANNEL.TYPES.ONE_TO_ONE }
                                },
                                {
                                    'member.user.name': { $autocomplete: searchString },
                                },
                                {
                                    $or: [
                                        {
                                            isFriends: { $eq: false },
                                            last_message_at: { $lte: moment(time).add(2, 'hours') },
                                            member_count: { $eq: 2 }
                                        },
                                        {
                                            isFriends: { $eq: true },
                                            member_count: { $eq: 2 }
                                        }
                                    ]
                                },
                            ]
                        },
                        {
                            $and: [
                                {
                                    type: {
                                        $in: [
                                            streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
                                            streamOptions.CHANNEL.TYPES.SYSTEM_PRIVATE_NETWORK,
                                            streamOptions.CHANNEL.TYPES.SYSTEM_THOUSAND_GREENS_PUBLIC,
                                            streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
                                            streamOptions.CHANNEL.TYPES.MY_TG_GROUP
                                        ]
                                    }
                                },
                                {
                                    name: { $autocomplete: searchString },
                                }
                            ]
                        },
                        { hidden: { $eq: true } },
                        { hidden: { $eq: false } }
                    ],
                }
            } else {
                return {
                    members: { $in: [userId] },
                    $or: [
                        {
                            $and: [
                                {
                                    type: { $eq: streamOptions.CHANNEL.TYPES.ONE_TO_ONE }
                                },
                                {
                                    $or: [
                                        {
                                            isFriends: { $eq: false },
                                            last_message_at: { $lte: moment(time).add(2, 'hours') },
                                            member_count: { $eq: 2 }
                                        },
                                        {
                                            isFriends: { $eq: true },
                                            member_count: { $eq: 2 }
                                        }
                                    ]
                                },
                            ]
                        },
                        {
                            type: {
                                $in: [
                                    streamOptions.CHANNEL.TYPES.USER_CREATED_GROUP,
                                    streamOptions.CHANNEL.TYPES.SYSTEM_PRIVATE_NETWORK,
                                    streamOptions.CHANNEL.TYPES.SYSTEM_THOUSAND_GREENS_PUBLIC,
                                    streamOptions.CHANNEL.TYPES.ADMIN_CREATED_GROUP,
                                    streamOptions.CHANNEL.TYPES.MY_TG_GROUP
                                ]
                            }
                        },
                        { hidden: { $eq: true } },
                        { hidden: { $eq: false } }
                    ]
                }
            }

        },
        DEFAULT_SORT: [{ last_message_at: -1 }]
    },
    FORWARD_MESSAGE_USER_LIMIT: 10,
    CHANNEL_ROLES: {
        CHANNEL_MODERATOR: "channel_moderator",
        CHANNEL_MEMBER: "channel_member"
    },
    CHAT_LIST_CONSTANTS: {
        MAIN_STREAM_CHANNEL_LIST: -1,
        NEW_1_TO_1_CHAT: 0,
        NEW_GROUP: 1,
        NEW_GROUP_FORM: 2,
        ARCHIVED: 3,
        BLOCKED: 4
    },
    GROUP_PHOTO_SIZE_LIMIT: 5 * 1024 * 1024,
    GET_STREAM_EVENT_TYPE: {
        MESSAGE_NEW: 'message.new'
    },
    CHAT_TYPES: [
        {
            label: 'All',
            value: 'all'
        },
        {
            label: 'Group',
            value: 'group'
        },
        {
            label: 'DM',
            value: 'dm'
        }
    ]
}

export default streamOptions;